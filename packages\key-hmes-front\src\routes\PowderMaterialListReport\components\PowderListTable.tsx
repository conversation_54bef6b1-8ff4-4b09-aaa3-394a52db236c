/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-05-27 15:02:30
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-09-23 10:37:32
 * @FilePath: \dst-front\packages\key-hmes-front\src\routes\PowderMaterialListReport\components\PowderListTable.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useEffect, useMemo, useState } from 'react';
import {
  Table,
  DataSet,
  Button,
  Modal,
  Form,
  TextField,
  Select,
  Lov,
  Tooltip,
  CheckBox,
} from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import Record from 'choerodon-ui/pro/lib/data-set/Record';
import withProps from 'utils/withProps';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { useRequest } from '@components/tarzan-hooks';
import moment from 'moment';
import notification from 'utils/notification';
import { UpdatePowderBalance, ExecuteMaterialFeeding, CancelMaterialFeeding } from '../services';
import { powderListDS, balanceAdjustDS, returnMaterialDS, feedingPlanSelectionDS } from '../stores';
import styles from '../index.module.less';
import { usePowderMaterial } from '../contextsStore';

const modelPrompt = 'hspc.powderMaterialListReport';

const PowderListTable = props => {
  const {
    powderListDs,
    balanceAdjustDs,
    returnMaterialDs,
    feedingPlanSelectionDs,
    queryParams,
  } = props;
  const [selectedRow, setSelectedRow] = useState<Record | null>(null);
  const { piecePowderListData = [] } = usePowderMaterial();

  const { run: updatePowderBalance } = useRequest(UpdatePowderBalance(), {
    manual: true,
    needPromise: true,
  });

  const { run: executeMaterialFeeding } = useRequest(ExecuteMaterialFeeding(), {
    manual: true,
    needPromise: true,
  });

  const { run: cancelMaterialFeeding } = useRequest(CancelMaterialFeeding(), {
    manual: true,
    needPromise: true,
  });

  const processPowderMaterialData = (data: { rows?: any[] }) => {
    const dataList = data?.rows || [];

    // 按产线分组
    const productionLineGroups = new Map();
    dataList.forEach(item => {
      const lineKey = item.prodLineCode;
      if (!productionLineGroups.has(lineKey)) {
        productionLineGroups.set(lineKey, []);
      }
      productionLineGroups.get(lineKey).push(item);
    });

    const processedData: any[] = [];

    // 处理每个产线的数据
    productionLineGroups.forEach(lineItems => {
      // 按班组分组
      const teamGroups = new Map();
      lineItems.forEach(item => {
        const teamKey = `${item.shiftDate}-${item.shiftTeamName}-${item.shiftCode}`;
        if (!teamGroups.has(teamKey)) {
          teamGroups.set(teamKey, []);
        }
        teamGroups.get(teamKey).push(item);
      });

      // 处理每个班组的数据
      teamGroups.forEach(teamItems => {
        teamItems.forEach((item, index) => {
          processedData.push({ ...item, lineNumber: index + 1 });
        });
        // 添加班组小计行
        if (teamItems.length > 0) {
          const { prodLineCode, shiftDate, shiftTeamName, shiftCode } = teamItems[0];
          processedData.push({
            prodLineCode,
            shiftDate,
            shiftTeamName,
            shiftCode,
            isEmptyRow: true,
            className: styles.emptyRow,
          });
        }
      });

      // 添加产线合计行
      if (lineItems.length > 0) {
        const { prodLineCode } = lineItems[0];
        processedData.push({
          prodLineCode,
          isProductionLineTotal: true,
          className: styles.productionLineTotal,
        });
      }
    });

    return processedData;
  };

  const columns = useMemo(
    () => [
      {
        title: '批次',
        key: 'aggregation',
        aggregation: true,
        children: [
          {
            name: 'lot',
            width: 110,
          },
          {
            name: 'identification',
            width: 110,
          },
          {
            name: 'assembleQty',
            width: 100,
            renderer: ({ record }: { record?: any }) => {
              if (record.get('isProductionLineTotal')) {
                // 获取当前产线的所有记录
                const currentLineRecords = powderListDs.records.filter(
                  item =>
                    !item.get('isEmptyRow') &&
                    !item.get('isProductionLineTotal') &&
                    item.get('prodLineCode') === record.get('prodLineCode'),
                );

                // 计算当前产线的总投入量
                const calculateLineTotalAssembleQty = records => {
                  return records.reduce(
                    (total, record) => total + (record.get('assembleQty') || 0),
                    0,
                  );
                };

                const lineTotalAssembleQty = calculateLineTotalAssembleQty(currentLineRecords);
                return (
                  <span style={{ color: '#000', fontWeight: 'bold' }}>{lineTotalAssembleQty}</span>
                );
              }

              if (record.get('isEmptyRow')) {
                // 获取当前班组的所有记录
                const currentTeamRecords = powderListDs.records.filter(
                  item =>
                    !item.get('isEmptyRow') &&
                    !item.get('isProductionLineTotal') &&
                    item.get('prodLineCode') === record.get('prodLineCode') &&
                    item.get('shiftDate') === record.get('shiftDate') &&
                    item.get('shiftTeamName') === record.get('shiftTeamName') &&
                    item.get('shiftCode') === record.get('shiftCode'),
                );

                // 计算当前班组的总投入量
                const calculateTeamTotalAssembleQty = records => {
                  return records.reduce(
                    (total, record) => total + (record.get('assembleQty') || 0),
                    0,
                  );
                };

                const teamTotalAssembleQty = calculateTeamTotalAssembleQty(currentTeamRecords);
                return <span style={{ color: '#000' }}>{teamTotalAssembleQty}</span>;
              }
              return (
                <Tooltip title={record.get('assembleQty')}>{record.get('assembleQty')}</Tooltip>
              );
            },
          },
          {
            name: 'creationDate',
            width: 140,
          },
          {
            name: 'lineNumber',
            width: 70,
          },
        ],
      },
    ],
    [selectedRow],
  );

  const groups = useMemo(
    () => [
      {
        name: 'prodLineCode',
        align: 'left',
        showTooltip: true,
        type: 'column',
        columnProps: {
          width: 100,
          align: 'left',
          title: '产线编码',
          lock: false,
          showTooltip: true,
          renderer: ({ record }) =>
            record.get('isEmptyRow') ? (
              <span style={{ color: '#000' }}>空</span>
            ) : (
              <Tooltip title={record.get('prodLineCode')}>{record.get('prodLineCode')}</Tooltip>
            ),
        },
      },
      {
        name: 'shiftDate',
        align: 'center',
        showTooltip: true,
        type: 'column',
        columnProps: {
          width: 160,
          align: 'center',
          title: '班次日期',
          lock: false,
          showTooltip: true,
          renderer: ({ record }) => {
            if (record.get('isProductionLineTotal')) {
              return <span style={{ color: '#000', fontWeight: 'bold' }}>合计</span>;
            }
            return record.get('isEmptyRow') ? (
              <span style={{ color: '#000' }}></span>
            ) : (
              <Tooltip title={record.get('shiftDate')}>{record.get('shiftDate')}</Tooltip>
            );
          },
        },
      },
      {
        name: 'shiftTeamName',
        showTooltip: true,
        align: 'center',
        type: 'column',
        columnProps: {
          width: 90,
          align: 'center',
          title: '班组',
          lock: false,
          showTooltip: true,
          renderer: ({ record }) =>
            record.get('isEmptyRow') ? (
              <span style={{ color: '#000' }}></span>
            ) : (
              <Tooltip title={record.get('shiftTeamName')}>{record.get('shiftTeamName')}</Tooltip>
            ),
        },
      },
      {
        name: 'shiftCode',
        align: 'center',
        type: 'column',
        showTooltip: true,
        columnProps: {
          width: 100,
          align: 'center',
          title: '班次',
          lock: false,
          showTooltip: true,
          renderer: ({ record }) =>
            record.get('isEmptyRow') ? (
              <span style={{ color: '#000' }}></span>
            ) : (
              <Tooltip title={record.get('shiftCode')}>{record.get('shiftCode')}</Tooltip>
            ),
        },
      },
      {
        name: 'lastBalance',
        align: 'center',
        type: 'column',
        showTooltip: true,
        columnProps: {
          width: 70,
          align: 'center',
          title: '上班余留',
          lock: false,
          showTooltip: true,
          renderer: ({ record }) =>
            record.get('isEmptyRow') ? (
              <span style={{ color: '#000' }}>小计</span>
            ) : (
              <Tooltip title={record.get('lastBalance')}>{record.get('lastBalance')}</Tooltip>
            ),
        },
      },
      {
        name: 'currentBalance',
        align: 'center',
        showTooltip: true,
        type: 'column',
        width: 70,
        columnProps: {
          width: 70,
          align: 'center',
          title: '本班余留',
          lock: false,
          showTooltip: true,
          renderer: ({ record }) => (
            <Tooltip title={record.get('currentBalance')}>{record.get('currentBalance')}</Tooltip>
          ),
        },
      },
      {
        name: 'prodTaskNum',
        align: 'center',
        showTooltip: true,
        type: 'column',
        columnProps: {
          width: 130,
          align: 'center',
          title: '源单编码',
          lock: false,
          showTooltip: true,
          renderer: ({ record }) => {
            if (record.get('isEmptyRow') || record.get('isProductionLineTotal')) {
              return <span style={{ color: '#000', fontWeight: 'bold' }}>投入汇总</span>;
            }
            return <Tooltip title={record.get('prodTaskNum')}>{record.get('prodTaskNum')}</Tooltip>;
          },
        },
      },
      {
        name: 'materialName',
        showTooltip: true,
        align: 'center',
        type: 'column',
        columnProps: {
          width: 130,
          align: 'center',
          title: '物料名称',
          lock: false,
          showTooltip: true,
          renderer: ({ record }) => {
            if (record.get('isProductionLineTotal')) {
              // 获取当前产线的所有记录
              const currentLineRecords = powderListDs.records.filter(
                item =>
                  !item.get('isEmptyRow') &&
                  !item.get('isProductionLineTotal') &&
                  item.get('prodLineCode') === record.get('prodLineCode'),
              );

              // 计算当前产线的总投入量
              const calculateLineTotalAssembleQty = records => {
                return records.reduce(
                  (total, record) => total + (record.get('assembleQty') || 0),
                  0,
                );
              };

              const lineTotalAssembleQty = calculateLineTotalAssembleQty(currentLineRecords);
              return (
                <span style={{ color: '#000', fontWeight: 'bold' }}>{lineTotalAssembleQty}</span>
              );
            }

            if (record.get('isEmptyRow')) {
              // 获取当前班组的所有记录
              const currentTeamRecords = powderListDs.records.filter(
                item =>
                  !item.get('isEmptyRow') &&
                  !item.get('isProductionLineTotal') &&
                  item.get('prodLineCode') === record.get('prodLineCode') &&
                  item.get('shiftDate') === record.get('shiftDate') &&
                  item.get('shiftTeamName') === record.get('shiftTeamName') &&
                  item.get('shiftCode') === record.get('shiftCode'),
              );

              // 计算当前班组的总投入量
              const calculateTeamTotalAssembleQty = records => {
                return records.reduce(
                  (total, record) => total + (record.get('assembleQty') || 0),
                  0,
                );
              };

              const teamTotalAssembleQty = calculateTeamTotalAssembleQty(currentTeamRecords);
              return <span style={{ color: '#000' }}>{teamTotalAssembleQty}</span>;
            }
            return (
              <Tooltip title={record.get('materialName')}>{record.get('materialName')}</Tooltip>
            );
          },
        },
      },
    ],
    [],
  );

  useEffect(() => {
    const { dateFrom, dateTo, prodLineCode, shiftTeamName, shiftCode, flakeLot } = queryParams;
    if (!dateFrom || !dateTo || !prodLineCode) return;

    powderListDs.setQueryParameter('dateFrom', dateFrom);
    powderListDs.setQueryParameter('dateTo', dateTo);
    powderListDs.setQueryParameter('prodLineCode', prodLineCode);
    powderListDs.setQueryParameter('shiftTeamName', shiftTeamName);
    powderListDs.setQueryParameter('shiftCode', shiftCode);
    powderListDs.setQueryParameter('flakeLot', flakeLot);
    powderListDs.query().then(res => {
      if (res) {
        const processedData = processPowderMaterialData(res);
        powderListDs.loadData(processedData);
      }
    });
  }, [queryParams]);

  const handleAdjustClick = () => {
    if (!selectedRow) return;
    balanceAdjustDs.loadData([]);

    const selectedData = {
      prodLineCode: selectedRow.get('prodLineCode'),
      lastBalance: selectedRow.get('lastBalance'),
      balance: selectedRow.get('currentBalance'),
      shiftTeamName: selectedRow.get('shiftTeamName'),
      shiftCode: selectedRow.get('shiftCode'),
    };
    balanceAdjustDs.loadData([selectedData]);
    Modal.open({
      title: intl.get(`${modelPrompt}.balanceAdjust`).d('结余调整'),
      key: Modal.key(),
      destroyOnClose: true,
      children: (
        <Table
          dataSet={balanceAdjustDs}
          columns={[
            { name: 'prodLineCode', editor: false },
            { name: 'shiftTeamName', editor: false },
            { name: 'shiftCode', editor: false },
            { name: 'lastBalance', editor: false },
            { name: 'balance', editor: true },
          ]}
        />
      ),
      onOk: async () => {
        const data = balanceAdjustDs.toData()[0];
        const params = {
          prodLineId: selectedRow.get('prodLineId'),
          powderBalance: data.balance,
          shiftTeamActualId: selectedRow.get('shiftTeamActualId'),
        };
        await updatePowderBalance({ params }).then(res => {
          if (!res || !res.success) {
            return false;
          }
          notification.success({});
          setSelectedRow(null);
          const {
            dateFrom,
            dateTo,
            prodLineCode,
            shiftTeamName,
            shiftCode,
            flakeLot,
          } = queryParams;

          if (!dateFrom || !dateTo || !prodLineCode) {
            return;
          }

          powderListDs.setQueryParameter('dateFrom', dateFrom);
          powderListDs.setQueryParameter('dateTo', dateTo);
          powderListDs.setQueryParameter('prodLineCode', prodLineCode);
          powderListDs.setQueryParameter('shiftTeamName', shiftTeamName);
          powderListDs.setQueryParameter('shiftCode', shiftCode);
          powderListDs.setQueryParameter('flakeLot', flakeLot);
          powderListDs.query().then(res => {
            if (res) {
              const processedData = processPowderMaterialData(res);
              powderListDs.loadData(processedData);
            }
          });
        });
      },
    });
  };

  const handleRowClick = record => {
    setSelectedRow(record);
  };

  // 退料操作
  const handleReturnClick = () => {
    if (!selectedRow) return;

    const rowData = selectedRow.toData();
    rowData.cancelQty = rowData.assembleQty;
    returnMaterialDs.loadData([rowData]);
    Modal.open({
      title: intl.get(`${modelPrompt}.return.material`).d('退料'),
      key: Modal.key(),
      destroyOnClose: true,
      children: (
        <Form labelWidth={120} dataSet={returnMaterialDs}>
          <TextField name="prodTaskNum" disabled />
          <TextField name="shiftTeamName" disabled />
          <TextField name="materialName" disabled />
          <TextField name="lot" disabled />
          <TextField name="cancelQty" />
        </Form>
      ),
      onOk: async () => {
        const valRes = await returnMaterialDs.validate();
        if (!valRes) {
          return;
        }
        const data = returnMaterialDs.toData()[0];
        const maxQty = selectedRow.get('assembleQty');
        if (data.cancelQty > maxQty) {
          notification.error({ message: `退回数量不能大于${maxQty}` });
          return false;
        }
        const submitData = {
          materialLotId: selectedRow.get('materialLotId'),
          materialLotCode: selectedRow.get('materialLotCode'),
          cancelQty: data.cancelQty,
          lot: selectedRow.get('lot'),
          assembleQty: selectedRow.get('assembleQty'),
          bomId: selectedRow.get('bomId'),
          workOrderId: selectedRow.get('workOrderId'),
          siteId: selectedRow.get('siteId'),
          materialId: selectedRow.get('materialId'),
          bomComponentId: selectedRow.get('bomComponentId'),
          productionLineId: selectedRow.get('prodLineId'),
          taskId: selectedRow.get('taskId'),
          operationType: 'REPORT',
          shiftDate: moment(selectedRow?.get('shiftDate')).format('YYYY-MM-DD'),
          shiftCode: selectedRow?.get('shiftCode'),
          shiftTeamId: selectedRow?.get('shiftTeamId'),
        };

        await cancelMaterialFeeding({ params: submitData }).then(res => {
          if (!res || !res.success) {
            return false;
          }
          notification.success({});
          const {
            dateFrom,
            dateTo,
            prodLineCode,
            shiftTeamName,
            shiftCode,
            flakeLot,
          } = queryParams;
          if (!dateFrom || !dateTo || !prodLineCode) return;

          powderListDs.setQueryParameter('dateFrom', dateFrom);
          powderListDs.setQueryParameter('dateTo', dateTo);
          powderListDs.setQueryParameter('prodLineCode', prodLineCode);
          powderListDs.setQueryParameter('shiftTeamName', shiftTeamName);
          powderListDs.setQueryParameter('shiftCode', shiftCode);
          powderListDs.setQueryParameter('flakeLot', flakeLot);
          powderListDs.query();
        });
      },
    });
  };

  // 投料操作
  const handleFeedingPlan = () => {
    if (!selectedRow) return;

    // 获取当前选中行的产线ID
    const currentProdLineId = selectedRow.get('prodLineId');
    showMaterialSelectionModal(currentProdLineId);
  };

  const showMaterialSelectionModal = (_prodLineId: string) => {
    if (!piecePowderListData.length) return;

    // 设置 prodLineId 到 feedingPlanSelectionDs
    if (!feedingPlanSelectionDs.current) {
      feedingPlanSelectionDs.create({ prodLineId: _prodLineId, isPowderList: true });
    } else {
      feedingPlanSelectionDs.current.set('prodLineId', _prodLineId);
      feedingPlanSelectionDs.current.set('isPowderList', true);
    }

    // 获取第一个产线的数据，并筛选itemGroup为04SZF
    const firstProdLineId = piecePowderListData[0].prodLineId;
    const firstProdLineData = piecePowderListData.filter(
      item => item.prodLineId === firstProdLineId && item.itemGroup === '04SZF',
    );

    // 预处理数据，按源单编号、配方和材料分组
    const groupedData = firstProdLineData.reduce((acc, item) => {
      // 源单编号分组
      if (!acc[item.prodTaskNum]) {
        acc[item.prodTaskNum] = {
          prodTaskNum: item.prodTaskNum,
          formulas: {},
        };
      }

      // 配方分组
      if (!acc[item.prodTaskNum].formulas[item.bomName]) {
        acc[item.prodTaskNum].formulas[item.bomName] = {
          bomName: item.bomName,
          materials: [],
        };
      }

      // 添加材料
      acc[item.prodTaskNum].formulas[item.bomName].materials.push({
        materialCode: item.materialCode,
        materialId: item.materialId,
        materialName: item.materialName,
        uomName: item.uomName,
        unitQty: item.unitQty,
        bomComponentId: item.bomComponentId,
        bomId: item.bomId,
        workOrderId: item.workOrderId,
        taskId: item.taskId,
      });

      return acc;
    }, {});

    // 创建源单编号的选项数据集
    const sourceOrderDs = new DataSet({
      data: Object.keys(groupedData).map(value => ({
        value,
        meaning: value,
      })),
    });

    // 创建配方的选项数据集
    const bomSelectDs = new DataSet({
      data: [],
    });

    // 创建材料的选项数据集
    const materialSelectDs = new DataSet({
      data: [],
    });

    // Modal.open 前确保有一条 record
    if (!feedingPlanSelectionDs.current) {
      feedingPlanSelectionDs.create({});
    }

    Modal.open({
      title: intl.get(`${modelPrompt}.feeding.material`).d('投料'),
      key: Modal.key(),
      destroyOnClose: true,
      children: (
        <Form labelWidth={120} dataSet={feedingPlanSelectionDs}>
          <Select
            name="prodTaskNum"
            options={sourceOrderDs}
            onChange={(value: string) => {
              const bomOptions = Object.keys(groupedData[value].formulas).map(bomName => ({
                value: bomName,
                meaning: bomName,
              }));
              bomSelectDs.loadData(bomOptions);
              feedingPlanSelectionDs.setQueryParameter('selectedSourceOrder', value);
              if (feedingPlanSelectionDs.current) {
                feedingPlanSelectionDs.current.set('bomName', null);
                feedingPlanSelectionDs.current.set('materialCode', null);
              }
            }}
          />
          <Select
            name="bomName"
            options={bomSelectDs}
            onChange={(value: string) => {
              const sourceOrder =
                feedingPlanSelectionDs.current && feedingPlanSelectionDs.current.get('prodTaskNum');
              const materialsArr =
                groupedData[sourceOrder] &&
                groupedData[sourceOrder].formulas[value] &&
                Array.isArray(groupedData[sourceOrder].formulas[value].materials)
                  ? groupedData[sourceOrder].formulas[value].materials
                  : [];
              // 按 materialId 去重
              const uniqueMaterials = Array.from(
                new Map(materialsArr.map((item: any) => [item.materialId, item])).values(),
              );
              const materialOptions = uniqueMaterials.map((material: any) => ({
                value: material.materialId,
                meaning: `${material.materialName}`,
              }));
              materialSelectDs.loadData(materialOptions);

              feedingPlanSelectionDs.setQueryParameter('selectedBom', value);
              if (feedingPlanSelectionDs.current) {
                feedingPlanSelectionDs.current.set('materialCode', null);
              }
            }}
          />
          <Select
            name="materialCode"
            options={materialSelectDs}
            primitiveValue={false}
            onChange={(value: any) => {
              if (feedingPlanSelectionDs.current) {
                feedingPlanSelectionDs.current.set('materialId', value.value);
                feedingPlanSelectionDs.current.set('materialLov', null);
              }
            }}
          />
          <Lov name="materialLov" />
          <TextField name="lot" />
          <TextField name="feedingQty" />
          <CheckBox name="powerFlag" />
        </Form>
      ),
      onOk: async () => {
        const valRes = await feedingPlanSelectionDs.validate();
        if (!valRes) {
          return;
        }

        const formData = feedingPlanSelectionDs.toData()[0];
        const sourceOrder = formData.prodTaskNum;
        const bomName = formData.bomName;
        const materialCode =
          formData.materialCode && formData.materialCode.value
            ? formData.materialCode.value
            : formData.materialCode;

        if (sourceOrder && bomName && materialCode) {
          const selectedMaterial = groupedData[sourceOrder].formulas[bomName].materials.find(
            material => material.materialId === materialCode,
          );

          if (selectedMaterial) {
            const submitData = {
              materialId: selectedMaterial.materialId,
              materialCode: selectedMaterial.materialCode,
              materialName: selectedMaterial.materialName,
              identifyType: 'MATERIAL_LOT',
              lot: formData.lot || null,
              primaryUomQty: formData.feedingQty,
              powerFlag: 'Y',
              bomComponentId: selectedRow?.get('bomComponentId'),
              materialLotId: formData.materialLov?.materialLotId,
              soundproofMaterialFlag: 'N',
              investQty: formData.feedingQty,
              checkBoxFlag: formData.powerFlag,
              bomId: selectedRow?.get('bomId'),
              workOrderId: selectedRow?.get('workOrderId'),
              siteId: selectedRow?.get('siteId'),
              taskId: selectedRow?.get('taskId'),
              productionLineId: _prodLineId,
              operationType: 'REPORT',
              shiftDate: moment(selectedRow?.get('shiftDate')).format('YYYY-MM-DD'),
              shiftCode: selectedRow?.get('shiftCode'),
              shiftTeamId: selectedRow?.get('shiftTeamId'),
            };

            await executeMaterialFeeding({ params: submitData }).then(res => {
              if (!res || !res.success) {
                return false;
              }
              notification.success({ message: '投料成功' });
              powderListDs.query().then(res => {
                if (res) {
                  const processedData = processPowderMaterialData(res);
                  powderListDs.loadData(processedData);
                }
              });
            });
          }
        }
      },
      afterClose: () => {
        feedingPlanSelectionDs.reset();
        feedingPlanSelectionDs.removeAll();
      },
    });
  };

  return (
    <div className={`${styles.tableBlock} ${styles.powderList}`}>
      <div className={styles.tableTitle}>粉料清单</div>
      <div className={styles.tableTitleLine}>
        <Button
          className={styles.powderListBtn}
          onClick={handleAdjustClick}
          color={ButtonColor.default}
          disabled={!selectedRow}
        >
          结余调整
        </Button>
        <Button
          className={styles.powderListBtn}
          disabled={!selectedRow}
          onClick={handleFeedingPlan}
        >
          投料
        </Button>
        <Button
          className={styles.powderListBtn}
          disabled={!selectedRow}
          onClick={handleReturnClick}
        >
          退料
        </Button>
      </div>
      <div className={`${styles.tableContent}`}>
        <Table
          columns={columns}
          dataSet={powderListDs}
          border
          pagination={false}
          groups={groups as any}
          onRow={({ record }) => ({
            onClick: () => {
              if (record.get('isEmptyRow')) {
                return;
              }
              handleRowClick(record);
            },
            className:
              record.get('className') || (record === selectedRow ? styles.selectedRow : ''),
          })}
        />
      </div>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.mes.event.powderListTable', 'tarzan.common'],
})(
  withProps(
    () => {
      const powderListDs = new DataSet({
        ...powderListDS(),
      });
      const balanceAdjustDs = new DataSet({
        ...balanceAdjustDS(),
      });
      const returnMaterialDs = new DataSet({
        ...returnMaterialDS(),
      });
      const feedingPlanSelectionDs = new DataSet({
        ...feedingPlanSelectionDS(),
      });
      return {
        powderListDs,
        balanceAdjustDs,
        returnMaterialDs,
        feedingPlanSelectionDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(PowderListTable),
);
