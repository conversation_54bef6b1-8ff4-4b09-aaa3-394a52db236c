/*
 * @workOrder 胶膜工作台-翻卷工单卡片
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-10-29 13:52:47
 * @copyright Copyright (c) 2024 , Hand
 */
/* eslint-disable no-console */
import React, { useMemo, useEffect, useState, useRef } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet, Modal, Form, TextField, Button } from 'choerodon-ui/pro';
import moment from 'moment';
import { ColumnProps, TableQueryBarType } from 'choerodon-ui/pro/lib/table/interface';
import notification from 'utils/notification';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { LabelLayout } from 'choerodon-ui/pro/lib/form/enum';
import { useDataSetEvent } from 'utils/hooks';
import intl from 'utils/intl';
// import { TemplatePrintButton } from '../../../../components/tarzan-ui';
import TemplatePrintButton from '../../TemplatePrintButton';
import { CardLayout, useRequest, ONotification } from '../commonComponents';
import { useAdhesiveFilmWorkbench } from '../../contextsStore';
import { tableDS, FormDS, oldEoNumberFormDS, newEoNumberFormDS } from './stores';
import {
  DeleteTableLine,
  CreateNewEONumber,
  ExecuteTableLine,
  IotAutoQuery,
  DegradeExecute,
  EoInfoQuery,
  ChangeEoInfo,
} from './services';
import { fetchAndGeneratePrintTemplate } from '../../services/index';
import styles from './index.module.less';

const modelPrompt = 'tarzan.rollOverWorkOrder';

const RollOverWorkOrder = observer(props => {
  const { enterInfo } = useAdhesiveFilmWorkbench();
  const { run: deleteTableLine, loading: deleteTableLineLoading } = useRequest(DeleteTableLine(), {
    manual: true,
    needPromise: true,
  });
  const { run: createNewEONumber, loading: createNewEONumberLoading } = useRequest(
    CreateNewEONumber(),
    {
      manual: true,
      needPromise: true,
    },
  );
  const { run: executeTableLine, loading: executeTableLineLoading } = useRequest(
    ExecuteTableLine(),
    {
      manual: true,
      needPromise: true,
    },
  );
  const { run: iotAutoQuery } = useRequest(IotAutoQuery(), {
    manual: true,
    needPromise: true,
  });

  const { run: degradeExecute, loading: degradeExecuteLoading } = useRequest(DegradeExecute(), {
    manual: true,
    needPromise: true,
  });

  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const formDs = useMemo(() => new DataSet(FormDS()), []);
  const oldEoDs = useMemo(() => new DataSet(oldEoNumberFormDS()), []);
  const newEoDs = useMemo(() => new DataSet(newEoNumberFormDS()), []);
  const { run: eoInfoQuery, loading: eoInfoQueryLoading } = useRequest(EoInfoQuery(), {
    manual: true,
    needPromise: true,
  });
  const { run: changeEoInfo, loading: changeEoInfoLoading } = useRequest(ChangeEoInfo(), {
    manual: true,
    needPromise: true,
  });
  const [printStatus, setPrintStatus] = useState(false);
  const [printParams, setPrintParams] = useState<any>({});
  const [repPrintParams, setRepPrintParams] = useState<any>({});
  const [repPrintStatus, setRepPrintStatus] = useState(false);
  const print = useRef(null);
  const repPrint = useRef(null);
  const printTemplate = useRef('');

  useEffect(() => {
    if (enterInfo?.prodLineId) {
      tableDs.setQueryParameter('productLineId', enterInfo?.prodLineId);
      tableDs.setQueryParameter('startTime', moment(new Date()).format('YYYY-MM-DD 00:00:00'));
      tableDs.setQueryParameter('endTime', moment(new Date()).format('YYYY-MM-DD 23:59:59'));
      tableDs.query();
    }
  }, [enterInfo]);

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  }, []);

  // 打印
  useEffect(() => {
    if (printParams?.eoIdList) {
      setTimeout(() => {
        // @ts-ignore
        print.current?.print();
      }, 500);
    }
  }, [printParams]);

  const handleReprintCertificate = async () => {
    const selectList = tableDs.selected.map(item => item.toData());

    if (!selectList?.length || selectList.length > 1) {
      setRepPrintStatus(false);
      return;
    }

    const params = [
      {
        workOrderId: selectList[0].workOrderId,
        newEoId: selectList[0].newEoId,
      },
    ];

    const template = await fetchAndGeneratePrintTemplate(
      '/roll-work-order-card/print',
      params,
      'HME.ROLL_WORK_ORDER',
      'POST',
    );

    if (!template) {
      setRepPrintStatus(false);
      return;
    }

    printTemplate.current = template;

    setRepPrintStatus(true);
    // 打印
    setRepPrintParams(params);
  };

  useDataSetEvent(tableDs, 'select', handleReprintCertificate);
  useDataSetEvent(tableDs, 'selectAll', handleReprintCertificate);

  const listener = flag => {
    // 搜索条件监听
    if (tableDs.queryDataSet) {
      const handler = flag
        ? tableDs.queryDataSet.addEventListener
        : tableDs.queryDataSet.removeEventListener;
      handler.call(tableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
  };

  const handleQueryDataSetUpdate = ({ record }) => {
    tableDs.setQueryParameter(
      'startTime',
      moment(record.get('startTime')).format('YYYY-MM-DD 00:00:00'),
    );
    tableDs.setQueryParameter(
      'endTime',
      moment(record.get('endTime')).format('YYYY-MM-DD 23:59:59'),
    );
  };

  const handleGain = async dataSet => {
    const res = await iotAutoQuery({
      params: {
        workcellId: tableDs.selected.length ? tableDs.selected[0]?.get('workcellId') : null,
        tagCodeList: ['WEIGHT'],
      },
    });
    if (res?.success) {
      if (res.rows.length) {
        const totalWeight = res.rows.filter(item => item.tagCode === 'WEIGHT')[0].tagValue;
        dataSet.current?.set('actualWeight', totalWeight);
      } else {
        ONotification.error({ message: '未获取到设备采集信息，请检查！' });
        return false;
      }
    } else {
      ONotification.error({ message: res?.message });
      return false;
    }
  };

  // 新卷号生成
  const handleCreateNewEONumClick = () => {
    const data = tableDs.selected[0];
    formDs.loadData([data]);
    formDs.current?.set(
      'planWeight',
      Number(
        Number(data?.get('length') || 0) *
          Number(data?.get('width') || 0) *
          Number(data?.get('materialQkxs') || 0),
      )?.toFixed(2),
    );
    Modal.open({
      title: '新卷号生成',
      key: Modal.key(),
      style: {
        width: '40%',
      },
      contentStyle: {
        background: '#38708F',
        color: '#FFF',
      },
      className: styles.doneStepFlagModals,
      children: (
        <Form
          className={styles.modalFormExceptionReporting}
          labelLayout={LabelLayout.horizontal}
          columns={1}
          labelWidth={180}
          dataSet={formDs}
        >
          <TextField name="oldProdModel" />
          <TextField name="oldEoNumber" />
          <TextField name="newEoNumber" />
          <TextField name="planWeight" />
          <TextField
            name="actualWeight"
            restrict="0.1-9"
            suffix={
              <div
                id="buttonPrimaryGainBtn"
                style={{ cursor: 'pointer' }}
                onClick={() => handleGain(formDs)}
              >
                {intl.get(`${modelPrompt}.gain`).d('获取')}
              </div>
            }
          />
        </Form>
      ),
      onOk: async () => {
        const validate = await formDs.validate();
        if (!validate) {
          notification.warning({
            message: '存在必输字段，请检查！',
          });
          return false;
        }
        const data = formDs?.current?.toData();
        createNewEONumber({
          params: {
            ...data,
            siteId: enterInfo?.siteId,
            shiftTeamCode: enterInfo?.shiftTeamCode,
            shiftTeamId: enterInfo?.shiftTeamId,
            shiftDate: enterInfo?.shiftDate,
            shiftCode: enterInfo?.shiftCode,
          },
        }).then(async res => {
          if (res?.success) {
            notification.success({
              message: '操作成功',
              description: undefined,
            });
            await tableDs.query();
          }
        });
      },
    });
  };

  // 删除指令单
  const handleDeleteClick = () => {
    if (!tableDs.selected?.length) {
      return;
    }
    const comletedData = tableDs.selected?.filter(
      item => item?.get('workOrderStatus') === 'COMPLETED',
    );
    if (comletedData?.length) {
      const workOrderNumList = comletedData?.map(item => item?.get('workOrderNum')).join(',');
      return notification.warning({
        message: `降级工单${workOrderNumList}已完成，不允许取消，请检查！`,
      });
    }

    Modal.confirm({
      iconType: '',
      key: Modal.key(),
      contentStyle: {
        background: '#38708F',
      },
      className: styles.doneStepFlagModals,
      children: <span style={{ color: '#FFF' }}>是否将选中数据删除，请确认！</span>,
      onOk: () => {
        deleteTableLine({
          params: { workOrderIds: tableDs.selected.map(item => item?.get('workOrderId')) },
        }).then(async res => {
          if (!res?.failed) {
            notification.success({
              message: '操作成功',
              description: undefined,
            });
            await tableDs.query();
          }
        });
      },
    });
  };

  // 报检
  const handleExcuteClick = () => {
    if (!tableDs.selected?.length) {
      return;
    }
    const comletedData = tableDs.selected?.map(item => item.toData());
    executeTableLine({
      params: {
        ...comletedData[0],
        siteId: enterInfo?.siteId,
        shiftTeamCode: enterInfo?.shiftTeamCode,
        shiftTeamId: enterInfo?.shiftTeamId,
        shiftDate: enterInfo?.shiftDate,
        shiftCode: enterInfo?.shiftCode,
      },
    }).then(async res => {
      if (res?.success) {
        notification.success({
          message: '操作成功',
          description: undefined,
        });
        await tableDs.query();
      } else {
        notification.error({
          message: '操作失败',
          description: res?.message,
        });
      }
    });
  };

  // 降级执行
  const handleDowngradeExcuteClick = () => {
    if (!tableDs.selected?.length) {
      return;
    }

    const completedData = tableDs.selected?.map(item => {
      const { workOrderId } = item.toData();
      return {
        workOrderId,
        productionLineId: enterInfo?.prodLineId,
        shiftTeamId: enterInfo?.shiftTeamId,
        shiftDate: enterInfo?.shiftDate,
        shiftCode: enterInfo?.shiftCode,
      };
    });

    degradeExecute({
      params: completedData,
    }).then(async res => {
      if (res?.success) {
        notification.success({
          message: '操作成功',
          description: undefined,
        });
        await tableDs.query();
      } else {
        notification.error({
          message: '操作失败',
          description: res?.message,
        });
      }
    });
  };

  // 杂质变更
  const handleImpurityChangeClick = async () => {
    if (!tableDs.selected?.length) return;
    const workOrderId = tableDs.selected[0]?.get('workOrderId');
    const res = await eoInfoQuery({ params: { workOrderId } });
    if (!res || res?.failed) {
      notification.error({ message: res?.message || '查询失败' });
      return;
    }
    const payload = res?.rows || res;
    oldEoDs.loadData([
      {
        eoNumber: payload?.eoNumber,
        identification: payload?.identification,
        defectInformation1: payload?.defectInformation1,
        defectInformation2: payload?.defectInformation2,
        defectInformation3: payload?.defectInformation3,
        defectInformation4: payload?.defectInformation4,
        joint1: payload?.joint1,
        joint2: payload?.joint2,
      },
    ]);
    newEoDs.loadData([
      {
        newEoNumber: payload?.newEoNumber,
        newIdentification: payload?.newIdentification,
        newDefectInformation1: payload?.newDefectInformation1,
        newDefectInformation2: payload?.newDefectInformation2,
        newDefectInformation3: payload?.newDefectInformation3,
        newDefectInformation4: payload?.newDefectInformation4,
        newJoint1: payload?.newJoint1,
        newJoint2: payload?.newJoint2,
      },
    ]);

    Modal.open({
      title: '杂质变更',
      key: Modal.key(),
      style: { width: '70%' },
      contentStyle: { background: '#38708F', color: '#FFF' },
      className: styles.doneStepFlagModals,
      children: (
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div>
            <Form
              className={styles.modalFormExceptionReporting}
              labelLayout={LabelLayout.horizontal}
              columns={1}
              labelWidth={180}
              dataSet={oldEoDs}
              header="原卷号"
            >
              <TextField name="eoNumber" />
              <TextField name="identification" />
              <TextField name="defectInformation1" />
              <TextField name="defectInformation2" />
              <TextField name="defectInformation3" />
              <TextField name="defectInformation4" />
              <TextField name="joint1" />
              <TextField name="joint2" />
            </Form>
          </div>
          <div style={{ marginLeft: '10px' }}>
            <Form
              className={styles.modalFormExceptionReporting}
              labelLayout={LabelLayout.horizontal}
              columns={1}
              labelWidth={180}
              dataSet={newEoDs}
              header="新卷号"
            >
              <TextField name="newEoNumber" />
              <TextField name="newIdentification" />
              <TextField name="newDefectInformation1" />
              <TextField name="newDefectInformation2" />
              <TextField name="newDefectInformation3" />
              <TextField name="newDefectInformation4" />
              <TextField name="newJoint1" />
              <TextField name="newJoint2" />
            </Form>
          </div>
        </div>
      ),
      onOk: async () => {
        const oldData = oldEoDs?.current?.toData?.() || oldEoDs?.toData?.()[0] || {};
        const newData = newEoDs?.current?.toData?.() || newEoDs?.toData?.()[0] || {};
        const body = {
          workOrderId: tableDs.selected[0]?.get('workOrderId'),
          eoId: tableDs.selected[0]?.get('eoId') || payload?.eoId || null,
          eoNumber: oldData?.eoNumber ?? null,
          identification: oldData?.identification ?? null,
          defectInformation1: oldData?.defectInformation1 ?? null,
          defectInformation2: oldData?.defectInformation2 ?? null,
          defectInformation3: oldData?.defectInformation3 ?? null,
          defectInformation4: oldData?.defectInformation4 ?? null,
          joint1: oldData?.joint1 ?? null,
          joint2: oldData?.joint2 ?? null,
          newEoId: tableDs.selected[0]?.get('newEoId') || payload?.newEoId || null,
          newEoNumber: newData?.newEoNumber ?? null,
          newIdentification: newData?.newIdentification ?? null,
          newDefectInformation1: newData?.newDefectInformation1 ?? null,
          newDefectInformation2: newData?.newDefectInformation2 ?? null,
          newDefectInformation3: newData?.newDefectInformation3 ?? null,
          newDefectInformation4: newData?.newDefectInformation4 ?? null,
          newJoint1: newData?.newJoint1 ?? null,
          newJoint2: newData?.newJoint2 ?? null,
        };
        const resp = await changeEoInfo({ params: body });
        if (resp?.success) {
          notification.success({ message: '操作成功' });
          await tableDs.query();
        } else {
          notification.error({ message: resp?.message || '操作失败' });
          return false;
        }
      },
    });
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        title: '序号',
        width: 80,
        align: ColumnAlign.center,
        renderer: ({ record }) => {
          const index = (record?.index || 0) + 1;
          return index > 9 ? index : `0${index}`;
        },
      },
      {
        name: 'workOrderNum',
        width: 180,
        align: ColumnAlign.center,
      },
      {
        name: 'oldEoNumber',
        width: 220,
        align: ColumnAlign.center,
      },
      {
        name: 'workOrderStatusDesc',
        align: ColumnAlign.center,
      },
      {
        name: 'planStartTime',
        width: 180,
        align: ColumnAlign.center,
        renderer: ({ value }) => {
          return moment(value).format('YYYY-MM-DD');
        },
      },
      {
        name: 'oldProdModel',
        width: 150,
        align: ColumnAlign.center,
      },
      {
        name: 'newProdModel',
        width: 160,
        align: ColumnAlign.center,
      },
      {
        name: 'prodDesc',
        align: ColumnAlign.center,
      },
      {
        name: 'prodTargetValue',
        align: ColumnAlign.center,
      },
      {
        name: 'color',
        align: ColumnAlign.center,
      },
      {
        name: 'newEoNumber',
        align: ColumnAlign.center,
        width: 300,
      },
    ];
  }, []);

  const onRow = ({ record }) => {
    return {
      onClick: () => {
        tableDs.data.forEach(item => {
          item.isSelected = false;
        });
        record.isSelected = true;
      },
    };
  };

  const handlePrintException = async () => {
    const params = {
      eoIdList: tableDs?.selected[0]?.get('newEoId'),
    };

    const template = await fetchAndGeneratePrintTemplate(
      '/hme-production-report/certificate/print/query/ui',
      params,
      'HME.EXCEPRION_PRINT',
    );

    if (!template) {
      setPrintStatus(false);
      return;
    }

    printTemplate.current = template;

    setPrintStatus(true);
    setPrintParams(params);
  };

  return (
    <CardLayout.Layout
      spinning={
        deleteTableLineLoading ||
        createNewEONumberLoading ||
        executeTableLineLoading ||
        degradeExecuteLoading ||
        eoInfoQueryLoading ||
        changeEoInfoLoading
      }
      className={styles.exceptionReporting}
    >
      <CardLayout.Header
        className="SelfMutualInspectionHead"
        title={intl.get(`${modelPrompt}.title`).d('降级')}
        help={props?.cardUsage?.remark}
        // addonAfter={
        //   <div className={styles.topRight}>
        //     {enterInfo?.workStationName} {enterInfo?.productionLineCode} {enterInfo?.shiftTeamName} {enterInfo.userName}
        //   </div>
        // }
      />
      <CardLayout.Content className="SelfMutualInspectionForm">
        <Table
          id={styles.tableInnerField}
          dataSet={tableDs}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            queryFieldsLimit: 3,
          }}
          columns={columns}
          customizedCode="RollOverWorkOrder"
          style={{ height: 'calc(100% - 36px)' }}
          renderEmpty={() => <></>}
          rowHeight={30}
          onRow={onRow}
        />
        <div className={styles.cardFooter}>
          <div className={styles.printButton}>
            {/* <Button color={ButtonColor.primary} className={styles.buttonOk} disabled={tableDs.selected?.length !== 1 ||(tableDs.selected?.length === 1 && tableDs?.selected[0]?.get('newEoNumber'))} onClick={handleCreateNewEONumClick}>新卷号生成</Button>
            <Button color={ButtonColor.primary} className={styles.buttonOk} disabled={!tableDs.selected?.length} onClick={handleDeleteClick}>删除指令单</Button>
            <Button color={ButtonColor.primary} className={styles.buttonOk} disabled={!tableDs.selected?.length || tableDs.selected?.length !== 1} onClick={handleExcuteClick}>报检</Button> */}
            <Button
              color={ButtonColor.primary}
              disabled={
                !tableDs.selected?.length ||
                tableDs.selected?.length !== 1
              }
              onClick={handleImpurityChangeClick}
              loading={eoInfoQueryLoading}
            >
              杂质变更
            </Button>
            <Button
              color={ButtonColor.primary}
              disabled={
                !tableDs.selected?.length ||
                tableDs.selected?.length !== 1 ||
                tableDs.selected?.some(item => item.get('workOrderStatus') === 'COMPLETED')
              }
              onClick={handleDowngradeExcuteClick}
            >
              降级执行
            </Button>
            <Button
              style={{
                backgroundColor: tableDs.selected.length === 0 ? '#32617f' : '#00d4cd',
                marginRight: '10px',
              }}
              disabled={
                tableDs.selected?.length !== 1 ||
                (tableDs.selected?.length === 1 && !tableDs?.selected[0]?.get('newEoNumber')) ||
                !tableDs.selected?.every(item => item.get('workOrderStatus') === 'COMPLETED')
              }
              onClick={handlePrintException}
            >
              打印异常
            </Button>
            <div style={{ display: 'none' }}>
              {printStatus && (
                <TemplatePrintButton
                  printButtonCode={printTemplate.current}
                  ref={print}
                  disabled={
                    tableDs.selected?.length !== 1 ||
                    (tableDs.selected?.length === 1 && !tableDs?.selected[0]?.get('newEoNumber')) ||
                    !tableDs.selected?.every(item => item.get('workOrderStatus') === 'COMPLETED')
                  }
                  name={intl.get(`${modelPrompt}.exception.print`).d('打印异常')}
                  printParams={printParams}
                  icon=""
                  frUrl="/hme-production-report/certificate/print/query/ui"
                />
              )}
            </div>
            {repPrintStatus ? (
              <TemplatePrintButton
                printButtonCode={printTemplate.current}
                ref={repPrint}
                style={{
                  backgroundColor: `${tableDs.selected?.length !== 1 ||
                    (tableDs.selected?.length === 1 &&
                      !tableDs?.selected[0]?.get('newEoNumber'))} ? '#00d4cd' :#bfbfbf`,
                }}
                disabled={
                  tableDs.selected?.length !== 1 ||
                  (tableDs.selected?.length === 1 && !tableDs?.selected[0]?.get('newEoNumber')) ||
                  !tableDs.selected?.every(item => item.get('workOrderStatus') === 'COMPLETED')
                }
                name={intl.get(`${modelPrompt}.print`).d('打印合格证')}
                icon=""
                printParams={repPrintParams}
                frUrl="/roll-work-order-card/print"
                method="POST"
              />
            ) : (
              <Button className={styles.buttonsSuccess} disabled>
                补打合格证
              </Button>
            )}
          </div>
        </div>
      </CardLayout.Content>
    </CardLayout.Layout>
  );
});

export default RollOverWorkOrder;
