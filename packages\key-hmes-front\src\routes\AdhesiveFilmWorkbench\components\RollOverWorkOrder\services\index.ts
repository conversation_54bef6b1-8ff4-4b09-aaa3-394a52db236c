/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-17 13:43:36
 * @LastEditTime: 2023-07-17 14:11:07
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// IOT数据采集
export function IotAutoQuery(): object{
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-production-report/iot-auto/query/ui`,
    method: "POST",
  };
}

/**
 * 报检数据
 * @function ExecuteTableLine
 * @returns {object} fetch Promise
 */
export function ExecuteTableLine(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/roll-work-order-card/inspect/excute`,
    method: 'POST',
  };
};

/**
 * 删除数据
 * @function DeleteTableLine
 * @returns {object} fetch Promise
 */
export function DeleteTableLine(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-scroll-notices/cancel/work-order`,
    method: 'POST',
  };
}

/**
 * 新卷号生成按钮(
 * @function CreateNewEONumber
 * @returns {object} fetch Promise
 */
export function CreateNewEONumber(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/roll-work-order-card/creat/new-eo-number`,
    method: 'POST',
  };
}

/**
 * 降级执行
 * @function DegradeExecute
 * @returns {object} fetch Promise
 */
export function DegradeExecute(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/demotion-work-order-card/demoted/execution/for/ui`,
    method: 'POST',
  };
}

/**
 * 卷号资料查询（杂质变更弹窗）
 * @function EoInfoQuery
 * @returns {object} fetch Promise
 */
export function EoInfoQuery(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/demotion-work-order-card/eo-info/ui`,
    method: 'POST',
  };
}

/**
 * 杂质变更-确认提交
 * @function ChangeEoInfo
 * @returns {object} fetch Promise
 */
export function ChangeEoInfo(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/demotion-work-order-card/change/eo/info`,
    method: 'POST',
  };
}

