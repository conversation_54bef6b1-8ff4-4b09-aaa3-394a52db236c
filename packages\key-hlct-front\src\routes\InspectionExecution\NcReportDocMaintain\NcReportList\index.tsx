/**
 * @Description: 不良记录单管理平台 -主界面
 * @Author: <EMAIL>
 * @Date: 2023/3/7 13:55
 */
import React, { FC, useCallback, useMemo, useState, useEffect } from 'react';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { Collapse, Tag } from 'choerodon-ui';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSet, Dropdown, Form, Lov, Menu, Modal, Table } from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Button as PermissionButton } from 'components/Permission';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId } from 'utils/utils';
import { isNil } from 'lodash';
import { useDataSetEvent } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { Action } from 'choerodon-ui/es/trigger/enum';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import notification from 'utils/notification';
import { BASIC, API_HOST } from '@utils/config';
import { TemplatePrintButton } from '../../../../components/tarzan-ui';
import ReviewHisDrawerComponent from './ReviewHisDrawerComponent';
import { headDS, lineDS } from '../stores';
import { ChangeDocStatus, BatchReview, HandleDealConfig } from '../services';
import styles from './index.module.less';

const modelPrompt = 'tarzan.hwms.ncReportDocMaintain';
const tenantId = getCurrentOrganizationId();
const { Panel } = Collapse;

interface NcReportListProps extends RouteComponentProps {
  headDs: any;
  lineDs: DataSet;
  customizeTable: any;
  customizeForm: any;
}

interface SelectInfoProps {
  ncReportIds: Array<number>;
  ncReportStatus: Array<string>;
  reviewType: Array<string>;
  ncReviewStatus: Array<string>;
}

const NcReportList: FC<NcReportListProps> = props => {
  const {
    match: { path },
    headDs,
    lineDs,
    history,
    customizeTable,
  } = props;
  const { run: changeDocStatus, loading: changeDocStatusLoading } = useRequest(ChangeDocStatus(), {
    manual: true,
  });
  const { run: batchReview, loading: reviewLoading } = useRequest(BatchReview(), {
    manual: true,
  });
  const { run: handleDealConfig, loading: handleDealLoading } = useRequest(HandleDealConfig(), {
    manual: true,
  });
  const [selectInfo, setSelectInfo] = useState<SelectInfoProps>({
    ncReportIds: [],
    ncReportStatus: [],
    reviewType: [],
    ncReviewStatus: [],
  });
  const [disabledPrint, setDisabledPrint] = useState(true); // 是否可打印
  const [createMethod, setCreateMethod] = useState(''); // 头上当前行的创建方式
  const [disposalType, setDisposalType] = useState(''); // 头上当前行的处置方法
  const [headRecord, setHeadRecord] = useState<any>(null); // 头列表当前行数据
  const [flag, setFlag] = useState<Boolean>(true); // 批量处置disabled

  // useDataSetEvent(headDs.queryDataSet, 'update', ({ name, record }) => {
  //   const lovDs = headDs.queryDataSet
  //     .getField('materialLotOrEoLov')
  //     ?.getOptions(headDs.queryDataSet.current);
  //   switch (name) {
  //     case 'ncObjectType':
  //       record?.init('inspectObjectLov', undefined);
  //       break;
  //     case 'materialLotOrEoLov':
  //       lovDs?.queryDataSet?.loadData([]);
  //       lovDs?.loadData([]);
  //       break;
  //     default:
  //       break;
  //   }
  // });

  const handleUpdateSelect = () => {
    // 检验业务类型为“FQC-JM”（成品检验-胶膜）、“SEC-FQC”（二次检验-树脂粉）、“KT-FQC”（成品检验-客退）时，可勾选后点击【标签打印】按钮
    let isPrint = !headDs.selected.length;
    const _ncReportIds: Array<number> = [];
    const _ncReportStatus: Array<string> = [];
    const _reviewType: Array<string> = [];
    const _ncReviewStatus: Array<string> = [];
    const labelPrintTypeList: Array<string> = [];
    headDs.selected.forEach(_record => {
      const lablePrintType = _record.get("labelPrintType");
      if (!["FQC-JM", "KT-FQC"].includes(_record.get("inspectBusinessType")) ||  _record.get('ncReportStatus') !== 'COMPLETED' || _record.get('ncReviewStatus') !== 'REVIEWED' || _record.get('disposalFunction') === 'PASS') {
        isPrint = true;
      }
      if (lablePrintType && !labelPrintTypeList.includes(lablePrintType)) {
        labelPrintTypeList.push(lablePrintType);
      }
      _ncReportIds.push(_record.get('ncReportId'));
      if (!_ncReportStatus.includes(_record.get('ncReportStatus'))) {
        _ncReportStatus.push(_record.get('ncReportStatus'));
      }
      if (!_reviewType.includes(_record.get('reviewType'))) {
        _reviewType.push(_record.get('reviewType'));
      }
      if (!_ncReviewStatus.includes(_record.get('ncReviewStatus'))) {
        _ncReviewStatus.push(_record.get('ncReviewStatus'));
      }
    });
    // setPrintButtonCodes(labelPrintTypeList);
    setDisabledPrint(isPrint);
    setSelectInfo({
      ncReportIds: _ncReportIds,
      ncReportStatus: _ncReportStatus,
      reviewType: _reviewType,
      ncReviewStatus: _ncReviewStatus,
    });
  };

  useDataSetEvent(headDs, 'batchSelect', handleUpdateSelect);
  useDataSetEvent(headDs, 'batchUnselect', handleUpdateSelect);

  useEffect(() => {
    headDs.addEventListener('select', handleDataSetSelectUpdate);
    headDs.addEventListener('unSelect', handleDataSetSelectUpdate);
    headDs.addEventListener('selectAll', handleDataSetSelectUpdate);
    headDs.addEventListener('unSelectAll', handleDataSetSelectUpdate);
    return () => {
      headDs.removeEventListener('select', handleDataSetSelectUpdate);
      headDs.removeEventListener('unSelect', handleDataSetSelectUpdate);
      headDs.removeEventListener('selectAll', handleDataSetSelectUpdate);
      headDs.removeEventListener('unSelectAll', handleDataSetSelectUpdate);
    };
  }, []);

  const handleDataSetSelectUpdate = () => {
    const flag = (headDs.selected.length >0 && headDs.selected?.every( item => {
      return item.get('ncReviewStatus') === 'UNREVIEWED' && 
      item.get('createMethod') === 'QMS' &&
      item.get('reviewType') === 'MANUAL_REVIEW';
    }) )|| false
    setFlag(!flag)
  }

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      queryLineTable(dataSet?.current.get('ncReportId'));
      setCreateMethod(dataSet?.current.get('createMethod'));
      setDisposalType(dataSet?.current.get('disposalType'));
      setHeadRecord(dataSet?.current.get('inspectBusinessType'));
    } else {
      queryLineTable(null);
    }
  };

  useDataSetEvent(headDs, 'load', resetHeaderDetail);

  const queryLineTable = ncReportId => {
    if (ncReportId) {
      lineDs.setQueryParameter('ncReportId', ncReportId);
    } else {
      lineDs.setQueryParameter('ncReportId', 0);
    }
    lineDs.query();
  };

  const renderNcReportTag = (value, record) => {
    switch (record.get('ncReportStatus')) {
      case 'NEW':
        return <Tag color="green">{value}</Tag>;
      case 'HANDLE':
        return <Tag color="magenta">{value}</Tag>;
      case 'COMPLETED':
        return <Tag color="orange">{value}</Tag>;
      case 'CANCEL':
        return <Tag color="gray">{value}</Tag>;
      default:
        return null;
    }
  };

  const renderNcReviewTag = (value, record) => {
    switch (record.get('ncReviewStatus')) {
      case 'REVIEWING':
        return <Tag color="orange">{value}</Tag>;
      case 'REVIEWED':
        return <Tag color="magenta">{value}</Tag>;
      case 'UNREVIEWED':
        return <Tag color="blue">{value}</Tag>;
      case 'REJECT':
        return <Tag color="green">{value}</Tag>;
      default:
        return null;
    }
  };

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'ncReportNum',
        lock: ColumnLock.left,
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(`/hwms/ncReport-doc-maintain/dist/${record!.get('ncReportId')}`);
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'ncReportTypeDesc', width: 130 },
      {
        name: 'ncReportStatusDesc',
        width: 130,
        renderer: ({ value, record }) => renderNcReportTag(value, record),
      },
      { name: 'siteName' },
      { name: 'createMethodDesc' },
      {
        name: 'ncReviewStatusDesc',
        renderer: ({ value, record }) => renderNcReviewTag(value, record),
      },
      { name: 'inspectBusinessTypeDesc', width: 150 },
      { name: 'objectCodes', width: 150 },
      { name: 'reviewTypeDesc' },
      { name: 'disposalTypeDesc' },
      { name: 'materialCode' },
      { name: 'materialName' },
      { name: 'revisionCode' },
      { name: 'inspectSumQty' },
      { name: 'okQty' },
      { name: 'ngQty' },
      { name: 'samplingQty' },
      { name: 'remark' },
      { name: 'ncDescription' },
      { name: 'createdByUserName' },
      { name: 'creationDate', align: ColumnAlign.center, width: 150 },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        width: 150,
        renderer: ({ record }) => (
          <PermissionButton
            type="text"
            permissionList={[
              {
                code: `list.button.reviewHistory`,
                type: 'button',
                meaning: '列表页-审批历史按钮',
              },
            ]}
            onClick={() => handleOpenHistoryDrawer(record)}
          >
            {intl.get(`${modelPrompt}.operation.reviewHistory`).d('审批历史')}
          </PermissionButton>
        ),
      },
    ];
  }, []);

  const handleOpenHistoryDrawer = record => {
    Modal.open({
      ...drawerPropsC7n({
        canEdit: false,
      }),
      title: intl.get(`${modelPrompt}.title.reviewHistory`).d('审批历史'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      children: <ReviewHisDrawerComponent record={record} customizeTable={customizeTable} />,
    });
  };

  const lineColumns: ColumnProps[] = useMemo(() => {
    return [
      { name: 'ncRecordTypeDesc', width: 120, lock: ColumnLock.left },
      { name: 'ncObjectTypeDesc', width: 120, lock: ColumnLock.left },
      { name: 'ncObjectCode', width: 120 },
      { name: 'revisionCode', width: 120},
      { name: 'objectCodes', 
        width: 120,
        renderer: ({ value, record })=>{
          if (record!.get('ncObjectTypeDesc') === '检验单') {
            return value
          }
        },
      },
      {
        name: 'materialCode',
        width: 120,
        renderer: ({ value, record }) => {
          if (record!.get('ncObjectType') === 'MATERIAL_LOT') {
            return value
          }
        },
      },
      {
        name: 'materialName',
        width: 120,
        renderer: ({ value, record }) => {
          if (record!.get('ncObjectType') === 'MATERIAL_LOT') {
            return value
          }
        },
      },
      headRecord === 'SEC-FQC' && { name: 'mergeLot', width: 120 },
      headRecord === 'SEC-FQC' && { name: 'mergeMaterialName', width: 120 },
      {
        name: 'sourceDocNum',
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push({
                  pathname: `/hwms/inspect-doc-maintain/list`,
                  state: {
                    sourceDocId: record!.get('sourceDocId'),
                    sourceDocNum: record!.get('sourceDocNum'),
                  },
                });
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'sourceNcRecordNum',
        width: 150,
        renderer: ({ value }) => {
          return (
            <a
              onClick={() => {
                history.push({
                  pathname: `/hmes/bad-record/platform/num/${value}`,
                  state: {
                    ncReportDocFlag: 'Y',
                  },
                });
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'lot' },
      { name: 'supplierLot' },
      { name: 'supplierName' },
      { name: 'customerName' },
      { name: 'containerName', width: 150 },
      { name: 'qty' },
      { name: 'uomName' },
      { name: 'workcellName', width: 150 },
      { name: 'equipmentName' },
      { name: 'operationName' },
      { name: 'locatorName' },
      { name: 'shiftTeamCode' },
      { name: 'shiftDate', align: ColumnAlign.center, width: 150 },
      { name: 'shiftCode' },
      { name: 'remark' },
      { name: 'ncDesc' },
      { name: 'ncStartUserName' },
      { name: 'ncStartTime', align: ColumnAlign.center, width: 150 },
    ];
  }, [headRecord]);

  const handleAdd = useCallback(() => {
    history.push(`/hwms/ncReport-doc-maintain/dist/create`);
  }, []);

  // 批量发起工作流
  const handleBatchReview = () => {
    batchReview({
      params: {
        ncReportIds: selectInfo.ncReportIds,
        ncReportStatus: 'HANDLE',
      },
      onSuccess: () => {
        notification.success({});
        headDs.query(headDs.currentPage);
      },
    });
  };

  const approvalDs = new DataSet({
    autoCreate: true,
    fields: [
      {
        name: 'siteId',
      },
      {
        name: 'returnMaterialLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.returnMaterial`).d('回料名称'),
        lovCode: 'HME.RETURN_MATERIAL',
        textField: 'materialName',
        ignore: FieldIgnore.always,
        required: true,
        dynamicProps: {
          lovPara: ({ record }) => ({
            tenantId,
            siteId: record?.get('siteId'),
          }),
        },
      },
      {
        name: 'recycledMaterialId',
        bind: 'returnMaterialLov.materialId',
      },
      {
        name: 'recycledMaterialName',
        bind: 'returnMaterialLov.materialName',
      },
      {
        name: 'recycledMaterialCode',
        bind: 'returnMaterialLov.materialCode',
      },
      {
        name: 'colorRecycledMaterialLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.colorRecycledMaterialLov`).d('彩条回料名称'),
        lovCode: 'HME.RETURN_MATERIAL',
        textField: 'materialName',
        ignore: FieldIgnore.always,
        dynamicProps: {
          lovPara: ({ record }) => ({
            tenantId,
            siteId: record?.get('siteId'),
          }),
        },
      },
      {
        name: 'colorRecycledMaterialId',
        bind: 'colorRecycledMaterialLov.materialId',
      },
      {
        name: 'colorRecycledMaterialName',
        bind: 'colorRecycledMaterialLov.materialName',
      },
      {
        name: 'colorRecycledMaterialCode',
        bind: 'colorRecycledMaterialLov.materialCode',
      },
    ],
  });

  const handleDeal = () => {
    let flag = false;
    headDs.selected.forEach(item=>{
      if (
        (
          item.get('inspectBusinessType') === 'FQC-JM' ||
          item.get('inspectBusinessType') === 'FJ-FQC' ||
          (
            item.get('inspectBusinessType') === 'KT-FQC' &&
            item.get('materialCode')?.substring(0, 3) === "PC."
          )
        ) 
      && item.get('disposalFunction') === 'SCRAP') {
        flag = true;
      }
    })
    approvalDs.current?.set('siteId', headDs.selected[0].get('siteId'))
    
    Modal.confirm({
      title: '批量处置确认',
      children: (
        <>
          <Form dataSet={approvalDs}>
            <span>处置后结果将不再支持修改，请确认是否进行批量处置？</span>
            {flag && <span>若处置方法为报废，则回料名称为：</span>}
            {flag && <Lov name="returnMaterialLov" label=""/>}
            {flag && <span>彩条回料名称为：</span>}
            {flag && <Lov name="colorRecycledMaterialLov" label=""/>}
          </Form>
        </>
      ),
      onOk: async () => {
        const vFlag = await approvalDs.validate();
        if (flag && !vFlag) {
          return false;
        }
        const list = flag ? headDs.selected.map(item=>{
          return {
            ncReportId: item.get('ncReportId'),
            ncReportNum: item.get('ncReportNum'),
            recycledMaterialId: approvalDs.current?.get('recycledMaterialId'),
            colorRecycledMaterialId: approvalDs.current?.get('colorRecycledMaterialId'),
          }
        }) :headDs.selected.map(item=>{
          return {
            ncReportId: item.get('ncReportId'),
            ncReportNum: item.get('ncReportNum'),
          }
        })
        handleDealConfig({
          params: list,
          onSuccess: () => {
            notification.success({});
            headDs.unSelectAll()
            headDs.query(headDs.currentPage);
          },
        });
      },
    });
  };

  const headerRowClick = record => {
    setCreateMethod(record.get('createMethod'));
    setDisposalType(record.get('disposalType'));
    setHeadRecord(record.get('inspectBusinessType'));
    queryLineTable(record?.get('ncReportId'));
  };

  // 点击状态变更的回调
  const clickMenu = async key => {
    changeDocStatus({
      params: {
        ncReportIds: selectInfo.ncReportIds,
        ncReportStatus: key,
      },
      onSuccess: () => {
        notification.success({});
        headDs.query(headDs.currentPage);
        setSelectInfo({
          ncReportIds: [],
          ncReportStatus: [],
          reviewType: [],
          ncReviewStatus: [],
        });
      },
    });
  };

  const menu = (
    <Menu className={styles['split-menu']}>
      {selectInfo.ncReportStatus.length === 1 && selectInfo.ncReportStatus[0] === 'NEW' && (
        <Menu.Item key="CLOSED">
          <a target="_blank" rel="noopener noreferrer" onClick={() => clickMenu('HANDLE')}>
            {intl.get(`${modelPrompt}.menu.handle`).d('开始处理')}
          </a>
        </Menu.Item>
      )}
      {selectInfo.ncReportStatus.length === 1 && selectInfo.ncReportStatus[0] === 'NEW' && (
        <Menu.Item key="CANCEL">
          <a target="_blank" rel="noopener noreferrer" onClick={() => clickMenu('CANCEL')}>
            {intl.get(`${modelPrompt}.menu.cancel`).d('取消')}
          </a>
        </Menu.Item>
      )}
      {selectInfo.ncReportStatus.length === 1 && selectInfo.ncReportStatus[0] === 'HANDLE' && (
        <Menu.Item key="CLOSED">
          <a target="_blank" rel="noopener noreferrer" onClick={() => clickMenu('COMPLETED')}>
            {intl.get(`${modelPrompt}.menu.completed`).d('完成')}
          </a>
        </Menu.Item>
      )}
    </Menu>
  );
  const getExportQueryParams = () => {
    if (!headDs.queryDataSet || !headDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = headDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return queryParmas;
  };
  
  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('不良记录单管理平台')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <Dropdown
          overlay={menu}
          disabled={
            selectInfo.ncReportStatus.length !== 1 ||
            ['CANCEL', 'COMPLETED'].includes(selectInfo.ncReportStatus[0])
          }
          trigger={[Action.click]}
        >
          <PermissionButton
            loading={changeDocStatusLoading}
            disabled={
              selectInfo.ncReportStatus.length !== 1 ||
              ['CANCEL', 'COMPLETED'].includes(selectInfo.ncReportStatus[0])
            }
            permissionList={[
              {
                code: `list.button.changeStatus`,
                type: 'button',
                meaning: '列表页-状态变更按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.changeStatus`).d('状态变更')}
          </PermissionButton>
        </Dropdown>
        <PermissionButton
          type="c7n-pro"
          loading={handleDealLoading}
          disabled={flag}
          onClick={handleDeal}
          permissionList={[
            {
              code: `${path}.button.deal`,
              type: 'button',
              meaning: '列表页-批量处置',
            },
          ]}
        >
          {intl.get('tarzan.common.button.deal').d('批量处置')}
        </PermissionButton>
        <PermissionButton
          onClick={handleBatchReview}
          loading={reviewLoading}
          permissionList={[
            {
              code: `list.button.batchReview`,
              type: 'button',
              meaning: '列表页-批量发起工作流',
            },
          ]}
          disabled={
            selectInfo.reviewType.length !== 1 ||
            selectInfo.reviewType[0] !== 'MANUAL_REVIEW' ||
            selectInfo.ncReviewStatus.length !== 1 ||
            selectInfo.ncReviewStatus[0] !== 'UNREVIEWED'
          }
        >
          {intl.get(`${modelPrompt}.button.batchReview`).d('批量发起工作流')}
        </PermissionButton>
        <TemplatePrintButton
          disabled={!headDs?.selected?.length || disabledPrint}
          printButtonCode='HME.NC_INSPECT_DOC_PRINT'
          printParams={{
            ncReportIdStr: headDs.selected.map(item => item.get('ncReportId')).join(','),
          }}
          frUrl='/mt-nc-report/nc/report/print/ui'
        />
        <ExcelExport
          method="GET"
          // requestUrl={`${API_HOST}${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-report/export`}
          requestUrl={`${API_HOST}${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-report/export/ui?ids=2`}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          queryParams={getExportQueryParams}
        />
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_LIST.LIST`,
          },
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={headDs}
            columns={headColumns}
            searchCode="ncReportDocMaintain1"
            customizedCode="ncReportDocMaintain-listHeader"
            onRow={({ record }) => ({
              onClick: () => headerRowClick(record),
            })}
          />,
        )}
        <Collapse bordered={false} defaultActiveKey={['line']}>
          <Panel key="line" header={intl.get(`${modelPrompt}.title.line`).d('不良记录行')}>
            {customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_LIST.LINE.LIST`,
              },
              <Table
                dataSet={lineDs}
                columns={lineColumns}
                customizedCode="ncReportDocMaintain-listLine"
                filter={record => {
                  if (createMethod === 'QMS') {
                    return disposalType === 'ALL'
                      ? record.get('ncObjectType') === 'INSPECT_DOC'
                      : record.get('ncObjectType') !== 'INSPECT_DOC';
                  }
                  return true;
                }}
              />,
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(headDS());
      const lineDs = new DataSet(lineDS());
      return {
        headDs,
        lineDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [
        `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_LIST.QUERY`,
        `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_LIST.LIST`,
        `${BASIC.CUSZ_CODE_BEFORE}.NC_REPORT_LIST.LINE.LIST`,
      ],
    })(NcReportList as any),
  ),
);
