/**
 * 胶膜拼卷计划-入口文件
 * @date 2024-10-16
 * <AUTHOR> <<EMAIL>>
 */
import React, { useEffect, useState, useMemo } from 'react';
import {
  DataSet,
  Table,
  Modal,
  CheckBox,
  Lov,
  Button,
  TextField,
  Switch,
  NumberField,
  Select,
} from 'choerodon-ui/pro';
import { Collapse, Popconfirm } from 'choerodon-ui';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { useDataSetEvent } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import withProps from 'utils/withProps';
import { columnSort } from '@/utils/utils';
import { flow, isEmpty } from 'lodash';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';
import axios from 'axios';
import { ViewMode } from 'choerodon-ui/pro/lib/lov/enum';
import CreateWorkOrderModal from './components/CreateWorkOrderModal';
import { headerTableDS, lineTableDS, importDS, demandDetailDS } from './stores/ListDS';
import { createWorkOrderHeaderDS, createWorkOrderLineDS } from './stores/CreateWorkOrderModalDS';
import { TemplatePrintButton } from '../../components/tarzan-ui';

const { Panel } = Collapse;
const { Option } = Select;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.glueFilmSplicingPlan';

const lugeUrl = '';

const Order = props => {
  const {
    headerTableDs,
    lineTableDs,
    demandDetailDs,
    match: { path },
  } = props;

  const createWorkOrderHeaderDs = new DataSet(createWorkOrderHeaderDS());
  const createWorkOrderLineDs = new DataSet(createWorkOrderLineDS());
  const importDs = new DataSet(importDS());

  // 判断头搜索条件切换
  const [selectedStatus, setSelectedStatus] = useState([]);
  const [selectedAuditStatus, setSelectedAuditStatus] = useState([]);
  const [selectedCanChange, setSelectedCanChange] = useState([]);
  // 关闭按钮禁用
  const [closeDisabled, setCloseDisabled] = useState(false);
  const [canEdit, setCanEdit] = useState(false);
  const [headerDetail, setHeaderDetail] = useState({});
  const [currentColunm, setCurrentColunm] = useState('');
  const [closeShow, setCloseShow] = useState(true);
  const [lineCloseData, setLineCloseData] = useState([]);
  const [copyProductDisabled, setCopyProductDisabled] = useState(false);
  const [languageTypeGroup, setLanguageTypeGroup] = useState([]);

  useEffect(() => {
    axios({
      url: `/hpfm/v1/${tenantId}/lovs/value/batch?typeGroup=HME.SPLICING_LANGUAGE`,
      method: 'get',
    }).then(res => {
      if (res) {
        const { typeGroup } = res;
        setLanguageTypeGroup(typeGroup);
      }
    });
  }, []);

  const getStatus = languageTypeGroup.map(ele => (
    <Option key={ele.orderSeq} value={ele.value}>
      {ele.meaning}
    </Option>
  ));

  const handleSelectTable = () => {
    // 所有数据状态都为NEW时
    const flag =
      lineTableDs?.selected?.length > 0 &&
      lineTableDs?.selected?.every(item => item.get('status') === 'NEW');
    setCloseDisabled(flag);
  };

  const hanldleSelectLineTable = () => {
    const copyProductFlag = lineTableDs?.selected?.length === 1;

    setCopyProductDisabled(copyProductFlag);
  };

  // 返回页面时恢复选中项和当前项状态
  useEffect(() => {
    if (props?.history?.action === 'PUSH') {
      setHeaderDetail({});
      headerTableDs.query(props.headerTableDs.currentPage);
      handleHeaderTableChange({
        dataSet: headerTableDs,
      });
    }
  }, []);

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  // 生成行列表DS查询项
  const listener = flag => {
    // 列表交互监听
    if (headerTableDs) {
      const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(headerTableDs, 'load', resetHeaderDetail);
      handler.call(headerTableDs, 'batchSelect', handleHeaderTableChange);
      handler.call(headerTableDs, 'batchUnSelect', handleHeaderTableChange);
    }
    if (lineTableDs) {
      const handler = flag ? lineTableDs.addEventListener : lineTableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(lineTableDs, 'load', handleSelectTable);
      handler.call(lineTableDs, 'batchSelect', handleSelectTable);
      handler.call(lineTableDs, 'batchUnSelect', handleSelectTable);
      handler.call(lineTableDs, 'select', hanldleSelectLineTable);
      handler.call(lineTableDs, 'unSelect', hanldleSelectLineTable);
      handler.call(lineTableDs, 'selectAll', hanldleSelectLineTable);
      handler.call(lineTableDs, 'unSelectAll', hanldleSelectLineTable);
    }
  };

  useDataSetEvent(lineTableDs, 'update', ({ name }) => {
    setCurrentColunm(name);
  });

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    setHeaderDetail({});
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      headerRowClick(dataSet?.current);
    }
  };

  // 头列表事件
  const handleHeaderTableChange = () => {
    const _selectedStatus = headerTableDs?.selected?.map(item => item?.toData()?.status) || [];
    const _selectedCanChange =
      headerTableDs?.selected?.filter(
        item => item?.toData()?.auditStatus === '2' && item?.toData()?.status === 'RELEASED',
      ) || [];
    const _selectedAuditStatus =
      headerTableDs?.selected?.filter(item => item?.toData()?.auditStatus !== '2') || [];
    setSelectedStatus(_selectedStatus);
    setSelectedCanChange(_selectedCanChange);
    setSelectedAuditStatus(_selectedAuditStatus);
  };

  // 行列表数据查询
  const queryLineTable = data => {
    lineTableDs.setQueryParameter('makeOrderId', data?.makeOrderId);
    lineTableDs.query().then(res => {
      if (res.failed) {
        notification.error({
          message: res.message,
        });
      } else {
        // 存在关闭的行
        const closeData = res?.filter(item => item.status === 'CLOSED');
        setLineCloseData(closeData);
        // 默认不展示关闭的行
        const unCloseData = res?.filter(item => item.status !== 'CLOSED');
        lineTableDs.loadData(unCloseData);
      }
    });
    setCanEdit(false);
  };

  // 头列表配置
  const headerTableColumns = [
    {
      name: 'makeOrderCode',
      width: 170,
      lock: 'left',
    },
    {
      name: 'splicingPlanSheetDetailCode',
      width: 150,
    },
    {
      name: 'siteMeaning',
      width: 150,
    },
    {
      name: 'status',
      width: 100,
      renderer: ({ record }) => {
        const status = record.get('status');
        const statusMeaning = record.getField('status').getLookupText(status);
        return (
          <span
            style={{
              color:
                status === 'NEW'
                  ? '#1890ff'
                  : status === 'RELEASED' || status === 'EORELEASED'
                  ? '#52c41a'
                  : '#f5222d',
            }}
          >
            {statusMeaning}
          </span>
        );
      },
    },
    { name: 'workshop', width: 100 },
    { name: 'prodLineCodes', width: 150 },
    { name: 'date', width: 130 },
    {
      name: 'auditStatusMeaning',
      width: 100,
    },
    {
      name: 'schedulingRate',
      width: 100,
    },
    {
      name: 'creatorMeaning',
      width: 150,
    },
    {
      name: 'creationDate',
      width: 100,
    },
    {
      name: 'auditorMeaning',
      width: 150,
    },
    {
      name: 'auditorDate',
      width: 100,
    },
    {
      name: 'lastUpdaterMeaning',
      width: 140,
    },
    {
      name: 'lastUpdateDate',
    },
    {
      name: 'alterByMeaning',
    },
    {
      name: 'alterDate',
    },
    {
      name: 'alterReason',
    },
  ];

  // 渲染单元格
  const columnsCell = record => {
    let columnsColor = '';
    if (record.get('status') === 'CLOSED') {
      columnsColor = 'rgb(201 198 198)';
    }
    return {
      style: {
        backgroundColor: columnsColor,
      },
    };
  };

  const handleChineseAndEnglish = value => {
    const item = languageTypeGroup.filter(val => val.meaning === value);
    lineTableDs.current.set('chineseAndEnglish', item[0].meaning);
  };

  // 行信息表配置
  const lineTableColumns = [
    { name: 'workOrderNum', width: 150, onCell: ({ record }) => columnsCell(record) },
    { name: 'prodTaskNum', width: 110, onCell: ({ record }) => columnsCell(record) },
    {
      name: 'statusMeaning',
      sortable: columnSort('statusMeaning'),
      onCell: ({ record }) => columnsCell(record),
      renderer: ({ record }) => {
        const status = record.get('status');
        const statusMeaning = record.get('statusMeaning');
        return (
          <span
            style={{
              color:
                status === 'NEW'
                  ? '#1890ff'
                  : status === 'RELEASED' || status === 'EORELEASED'
                  ? '#52c41a'
                  : '#f5222d',
            }}
          >
            {statusMeaning}
          </span>
        );
      },
    },
    { name: 'soType', width: 110, onCell: ({ record }) => columnsCell(record) },
    {
      name: 'splicingPlanSheetDetailCode',
      width: 140,
      onCell: ({ record }) => columnsCell(record),
    },
    { name: 'materialCode', onCell: ({ record }) => columnsCell(record) },
    { name: 'materialName', onCell: ({ record }) => columnsCell(record) },
    { name: 'prodLineCode', onCell: ({ record }) => columnsCell(record) },
    {
      name: 'productionQuantity',
      width: 130,
      onCell: ({ record }) => columnsCell(record),
      renderer: ({ value }) => {
        return Number(value).toFixed(2); // 生产数量保留2位小数
      },
    },
    {
      name: 'productionMeter',
      onCell: ({ record }) => columnsCell(record),
      renderer: ({ value }) => {
        return Math.max(0, Math.floor(Number(value))); // 生产米数为正整数
      },
    },
    {
      name: 'metersStandradRoll',
      onCell: ({ record }) => columnsCell(record),
      editor: () => canEdit,
    },
    {
      name: 'metersPerRoll',
      onCell: ({ record }) => columnsCell(record),
      editor: () => canEdit,
    },
    { name: 'volume', onCell: ({ record }) => columnsCell(record) },
    { name: 'completedQty', onCell: ({ record }) => columnsCell(record) },
    { name: 'unspelledVolume', onCell: ({ record }) => columnsCell(record) },
    {
      name: 'prodTargetValueShow',
      onCell: ({ record }) => columnsCell(record),
      editor: () => canEdit,
    },
    { name: 'productGrade', onCell: ({ record }) => columnsCell(record) },
    {
      name: 'wide',
      editor: () => canEdit,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'printColorLov',
      width: 150,
      editor: () => canEdit && <Lov name="printColorLov" />,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'clientAppointProdLineLov',
      width: 150,
      editor: () => canEdit && <Lov name="clientAppointProdLineLov" />,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'reelOrderMarkers',
      width: 150,
      editor: () => canEdit && <Select name="reelOrderMarkers" searchable />,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'volumeLabel',
      editor: () => canEdit && <CheckBox name="volumeLabel" />,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'printingCustomerLov',
      editor: () => canEdit && <Lov name="printingCustomerLov" />,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'deliveryDestination',
      editor: () => canEdit,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'orderRequirement',
      editor: () => canEdit,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'prodTechnicalRequirements',
      editor: () => canEdit,
      onCell: ({ record }) => columnsCell(record),
      width: 150,
    },
    {
      name: 'remark',
      editor: () => canEdit,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'packingRequirement',
      editor: () => canEdit,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'packingMethodName',
      editor: () => canEdit,
      width: 150,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'packingMethodLov',
      width: 150,
      editor: () => canEdit && <Lov name="packingMethodLov" />,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'packingSpecificationLov',
      editor: () => canEdit && <Lov name="packingSpecificationLov" />,
      renderer: ({ value, record }) => {
        if (!value) return '';
        const packingSpecificationName = record.get('packingSpecificationName');
        return packingSpecificationName;
      },
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'containerLabel',
      editor: () => canEdit,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'isolationFilmSpecificationLov',
      editor: () => canEdit && <Lov name="isolationFilmSpecificationLov" />,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'printTemplate',
      editor: () => canEdit && <Select name="printTemplate" />,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'chineseAndEnglish',
      width: 130,
      editor: record =>
        canEdit && (
          <Select onChange={handleChineseAndEnglish} value={record.get('chineseAndEnglish')}>
            {getStatus}
          </Select>
        ),
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'productName',
      width: 130,
      editor: () => canEdit && <CheckBox name="productName" />,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'netWeight',
      width: 130,
      editor: () => canEdit && <CheckBox name="netWeight" />,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'grossWeight',
      width: 130,
      editor: () => canEdit && <CheckBox name="grossWeight" />,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'lastPageContent',
      width: 150,
      editor: () => canEdit && <CheckBox name="lastPageContent" />,
      onCell: ({ record }) => columnsCell(record),
    },
    {
      name: 'thickness',
      editor: () => canEdit && <CheckBox name="lastPageContent" />,
      onCell: ({ record }) => columnsCell(record),
    },
    // { name: 'auxiliaryAttribute', editor: (record)=> canEdit    , onCell: ({ record }) => columnsCell(record) },
  ];

  const headerRowClick = record => {
    setHeaderDetail(record);
    queryLineTable(record?.toData());
  };

  const clickMenu = async () => {
    const reasonDs = new DataSet({
      autoCreate: true,
      fields: [
        {
          name: 'alterReason',
          type: 'string',
          required: true,
          label: intl.get('tarzan.common.modal.label.changeReason').d('变更原因'),
        },
      ],
    });
    Modal.open({
      title: intl.get('tarzan.common.modal.label.alterReason').d('变更原因'),
      children: (
        <TextField
          dataSet={reasonDs}
          name="alterReason"
          placeholder={intl.get('tarzan.common.modal.label.alterReason').d('变更原因')}
          required
        />
      ),
      onOk: async () => {
        const validateResult = await reasonDs.validate();
        if (!validateResult) {
          notification.error({
            message: intl.get('tarzan.common.message.error.validate').d('请检查输入数据'),
          });
          return false;
        }
        const data =
          headerTableDs?.selected?.map(item => {
            return {
              ...item.toData(),
              alterReason: reasonDs.current.get('alterReason'),
            };
          }) || [];
        return request(
          `${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/hme-make-order/splicing/list/update/ui`,
          {
            method: 'POST',
            body: data,
          },
        ).then(res => {
          if (res && res.success) {
            headerTableDs.batchUnSelect(headerTableDs.selected);
            headerTableDs.clearCachedSelected();
            setSelectedStatus([]);
            setSelectedCanChange([]);
            setSelectedAuditStatus([]);
            headerTableDs.query(props.headerTableDs.currentPage);
            notification.success({
              message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
            });
          } else {
            notification.error({
              message: res?.message || res,
            });
            return false;
          }
        });
      },
      cancel: () => {},
    });
  };

  const handleCreateWorkOrder = async () => {
    await createWorkOrderHeaderDs.query();
    createWorkOrderLineDs.loadData([]);
    const addDrawer = Modal.open({
      key: Modal.key(),
      drawer: true,
      title: (
        <span style={{ padding: '1px' }}>
          {intl.get(`${modelPrompt}.createWorkOrder.title`).d('创建生产指令')}
        </span>
      ),
      style: {
        width: 1520,
      },
      contentStyle: {
        width: 1520,
        paddingTop: 0,
      },
      bodyStyle: {
        width: 1520,
        overflow: 'hidden',
        padding: '0 16px',
      },
      destroyOnClose: true,
      className: 'hmes-style-modal',
      children: (
        <CreateWorkOrderModal
          headerTableDs={createWorkOrderHeaderDs}
          lineTableDs={createWorkOrderLineDs}
        />
      ),
      onOk: async () => {
        const validate = await createWorkOrderLineDs.validate();
        if (!validate) {
          notification.error({
            message: intl.get(`${modelPrompt}.createWorkOrder.validate.error`).d('请检查输入项'),
          });
          return false;
        }
        const data = createWorkOrderLineDs.toData();
        if (data.length === 0) {
          notification.error({
            message: intl.get(`${modelPrompt}.createWorkOrder.length.error`).d('请选择订单信息'),
          });
          return false;
        }
        handleCreateWorkOrderOk(addDrawer);
      },
      onCancel: () => addDrawer.close(),
    });
  };

  const handleCreateWorkOrderOk = addDrawer => {
    const selectedData = headerTableDs.selected.map(item => item.toData()) || [];
    const data = createWorkOrderLineDs.toData().map(item => ({
      ...item,
      moId: headerTableDs.selected.length > 0 ? selectedData[0].makeOrderId : null,
    }));
    request(`${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/hme-make-order/list/save/data`, {
      method: 'POST',
      body: data,
    }).then(res => {
      if (res?.success) {
        notification.success({
          message: intl.get(`${modelPrompt}.createWorkOrder.success`).d('生产指令创建成功'),
        });
        headerTableDs.query(props.headerTableDs.currentPage);
        addDrawer.close();
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  const splicingApply = () => {
    const data = headerTableDs?.selected?.map(item => item.toData()) || [];

    return request(
      `${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/hme-make-order/splicing/plan/audit`,
      {
        method: 'POST',
        body: data,
      },
    ).then(res => {
      if (res && res?.failed) {
        notification.error({
          message: res?.message,
        });
      } else {
        headerTableDs.batchUnSelect(headerTableDs.selected);
        headerTableDs.clearCachedSelected();
        setSelectedStatus([]);
        setSelectedCanChange([]);
        setSelectedAuditStatus([]);
        headerTableDs.query(props.headerTableDs.currentPage);
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      }
    });
  };

  const onFieldEnterDown = () => {
    headerTableDs.query(props.headerTableDs.currentPage);
  };

  const handleToImport = value => {
    if (value) {
      const workOrderIdList = value.map(item => {
        return { workOrderId: item.workOrderId };
      });
      request(`${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/hme-make-order/line/list/new/ui`, {
        method: 'POST',
        body: workOrderIdList,
      }).then(res => {
        if (res?.success) {
          notification.success({
            message: intl
              .get(`${modelPrompt}.production.instructions.import.success`)
              .d('生产指令导入成功'),
          });
          headerTableDs.query(props.headerTableDs.currentPage);
        } else {
          notification.error({
            message: res?.message,
          });
        }
      });
    }
  };

  /**
   * @description 需求数量计算
   */
  const handleDemandCalculation = async () => {
    if (!lineTableDs.selected.length) {
      return notification.error({
        message: intl
          .get(`${modelPrompt}.selectError`)
          .d('当前未选择单据行，请先选择行再执行操作!'),
      });
    }
    const dataList = lineTableDs.selected.map(record => record.toData());
    const packFlagList = dataList.filter(item => item.packFlag === 'Y');
    if (packFlagList.length) {
      return notification.error({
        message: `生产指令单号${packFlagList[0].workOrderNum}已执行过需求计算，请重新选择！`,
      });
    }
    const errorList = dataList.filter(item => !item.packingSpecification);
    if (errorList.length) {
      return notification.error({
        message: `生产指令单号${errorList[0].workOrderNum}未填写箱装规格，请填写完整后再操作!`,
      });
    }
    const data = lineTableDs.selected.map(record => ({
      workOrderId: record.get('workOrderId'),
      volume: record.get('volume'),
      boxMaterialCode: record.get('packingSpecificationCode'),
    }));
    const res = await request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-make-order/select/box-material/list`,
      {
        method: 'POST',
        body: data,
      },
    );
    if (res?.success) {
      const data = res.rows.map(item => ({
        ...item,
        purchaseFlag: 'N',
      }));
      demandDetailDs.loadData(data);
    } else {
      return notification.error({ message: res?.message });
    }
    Modal.open({
      title: intl.get(`${modelPrompt}.demandDetail`).d('需求明细'),
      key: Modal.key(),
      destroyOnClose: true,
      style: {
        width: 1520,
      },
      drawer: true,
      children: <Table dataSet={demandDetailDs} columns={demandDetailColumns} />,
      onOk: handleModalSave,
      onCancel: () => {
        demandDetailDs.reset();
      },
    });
  };

  const handleModalSave = async () => {
    const validate = demandDetailDs.validate();
    if (!validate) {
      return false;
    }

    const res = await request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-scroll-notices/save/pack/calculation`,
      {
        method: 'POST',
        body: {
          noticeList: demandDetailDs.toData(),
        },
      },
    );
    if (res?.success) {
      lineTableDs.query();
      return true;
    }
    notification.error({ message: res?.message });
    return false;
  };

  const handleDemanDetailCreate = () => {
    let maxLineNumber = 0;
    demandDetailDs.toData().forEach(item => {
      const { lineNumber } = item;
      if (lineNumber >= maxLineNumber) {
        maxLineNumber = lineNumber;
      }
    });
    demandDetailDs.create({
      lineNumber: Math.floor(maxLineNumber / 10) * 10 + 10,
      boxDealStatusDesc: '新建',
      boxDealStatus: 'NEW',
      workOrderIds: demandDetailDs.toData()[0].workOrderIds,
    });
  };

  const demandDetailColumns = useMemo(() => {
    return [
      {
        header: (
          <Button
            icon="add"
            onClick={() => handleDemanDetailCreate()}
            funcType="flat"
            shape="circle"
            size="small"
          />
        ),
        name: 'editColumn',
        align: 'center',
        width: 70,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => {
              demandDetailDs.remove(record);
            }}
          >
            <Button funcType="flat" icon="remove" shape="circle" size="small" />
          </Popconfirm>
        ),
        lock: 'left',
      },
      {
        name: 'lineNumber',
      },
      {
        name: 'boxDealStatusDesc',
      },
      {
        name: 'boxMaterialLov',
        editor: record => (
          <Lov name="boxMaterialLov" onChange={val => hanleChange(record, val, 'needQty')} />
        ),
      },
      {
        name: 'boxMaterialName',
      },
      {
        name: 'boxMaterialModel',
      },
      {
        name: 'needQty',
      },
      {
        name: 'boxMaterialUomName',
      },
      {
        name: 'sourceLocatorLov',
        editor: record => (
          <Lov
            name="sourceLocatorLov"
            onChange={val => hanleChange(record, val, 'sourceLocatorQty')}
          />
        ),
      },
      {
        name: 'sourceLocatorQty',
        width: 120,
      },
      {
        name: 'targetLocatorLov',
        editor: record => (
          <Lov
            name="sourceLocatorLov"
            onChange={val => hanleChange(record, val, 'targetLocatorQty')}
          />
        ),
      },
      {
        name: 'targetLocatorQty',
        width: 120,
      },
      {
        name: 'transferQuantity',
        editor: true,
      },
      {
        name: 'purchaseFlag',
        editor: record => (
          <Switch name="purchaseFlag" onChange={val => purchaseFlagChange(val, record)} />
        ),
      },
      {
        name: 'purchaseQuantity',
        editor: true,
      },
    ];
  }, []);

  const purchaseFlagChange = (val, record) => {
    if (val === 'Y') {
      const purchaseQuantity =
        record.get('needQty') - record.get('targetLocatorQty') - record.get('sourceLocatorQty');
      record.set('purchaseQuantity', purchaseQuantity);
    } else {
      record.set('purchaseQuantity', null);
    }
  };

  const hanleChange = async (record, val, name) => {
    const res = await request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-scroll-notices/select/locator/qty`,
      {
        method: 'POST',
        body: {
          locatorId: val.locatorId || val.materialId,
          materialCode: record.get('boxMaterialCode'),
        },
      },
    );
    if (res?.success) {
      record.set(name, res.rows?.qty);
    } else {
      notification.error({ message: res?.message });
    }
    if (record.get('targetLocatorQty') && record.get('sourceLocatorQty')) {
      let transferQuantity = record.get('needQty') - record.get('targetLocatorQty');
      if (transferQuantity > record.get('sourceLocatorQty')) {
        transferQuantity = record.get('sourceLocatorQty');
      }
      record.set('transferQuantity', transferQuantity);
    }
  };

  /**
   * @description 行批量保存
   */
  const handleLineTableSave = async () => {
    // 只校验非关闭行的数据
    const flag = await lineTableDs.validate();
    if (flag) {
      return lineTableDs.submit().then(() => {
        setCanEdit(false);
        lineTableDs.query(props.lineTableDs.currentPage);
      });
    }
  };

  const handleLineTableClose = () => {
    const data = lineTableDs.selected.map(item => item.toData()) || [];
    request(`${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/hme-make-order/close/line`, {
      method: 'POST',
      body: data,
    }).then(res => {
      if (res?.success) {
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
        headerRowClick(headerDetail);
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  const handleLineTableCloseShow = () => {
    if (closeShow) {
      // 展示所有行，包括关闭的行
      lineTableDs.query(props.lineTableDs.currentPage);
    } else {
      // 只展示非关闭的行
      const unCloseData = lineTableDs?.toData()?.filter(item => item.status !== 'CLOSED') || [];
      lineTableDs.loadData(unCloseData);
    }
    setCloseShow(!closeShow);
  };

  // 生产目标值变更功能
  const handleProdTargetValueChange = () => {
    if (!lineTableDs.selected.length) {
      return notification.error({
        message: intl
          .get(`${modelPrompt}.selectError`)
          .d('当前未选择单据行，请先选择行再执行操作!'),
      });
    }

    const selectedRecord = lineTableDs.selected[0];
    const currentTargetValue = Number(selectedRecord.get('prodTargetValue')) || 0;
    const currentTargetValueShow = Number(selectedRecord.get('prodTargetValueShow')) || 0;

    const targetValueChangeDs = new DataSet({
      autoCreate: true,
      fields: [
        {
          name: 'originalTargetValue',
          type: 'number',
          label: intl.get(`${modelPrompt}.originalTargetValue`).d('变更前'),
          disabled: true,
        },
        {
          name: 'newTargetValue',
          type: 'number',
          label: intl.get(`${modelPrompt}.newTargetValue`).d('变更后'),
          required: true,
          min: 0,
          step: 0.001,
          precision: 3,
        },
      ],
    });

    targetValueChangeDs.loadData([
      {
        originalTargetValue: currentTargetValueShow,
      },
    ]);

    Modal.open({
      title: intl.get(`${modelPrompt}.prodTargetValueChange`).d('生产目标值变更'),
      style: {
        width: 420,
      },
      children: (
        <div style={{ padding: '20px 0' }}>
          <div style={{ display: 'flex', gap: '20px', alignItems: 'flex-start' }}>
            <div style={{ flex: 1 }}>
              <div
                style={{
                  marginBottom: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#333',
                }}
              >
                {intl.get(`${modelPrompt}.originalTargetValue`).d('变更前')}
              </div>
              <NumberField
                dataSet={targetValueChangeDs}
                name="originalTargetValue"
                style={{ width: '100%' }}
              />
            </div>
            <div style={{ flex: 1 }}>
              <div
                style={{
                  marginBottom: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#333',
                }}
              >
                {intl.get(`${modelPrompt}.newTargetValue`).d('变更后')}
              </div>
              <NumberField
                dataSet={targetValueChangeDs}
                name="newTargetValue"
                placeholder={intl
                  .get(`${modelPrompt}.newTargetValuePlaceholder`)
                  .d('请输入新的目标值')}
                style={{ width: '100%' }}
              />
            </div>
          </div>
        </div>
      ),
      onOk: async () => {
        const validateResult = await targetValueChangeDs.validate();
        if (!validateResult) {
          notification.error({
            message: intl.get('tarzan.common.message.error.validate').d('请检查输入数据'),
          });
          return false;
        }

        const newTargetValue = targetValueChangeDs.current.get('newTargetValue');
        const workOrderId = selectedRecord.get('workOrderId');

        return request(
          `${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/hme-make-order/prod/target/change`,
          {
            method: 'POST',
            body: {
              workOrderId,
              oldTargetValue: currentTargetValue,
              newTargetValue: Number((newTargetValue / 1000).toFixed(6)),
            },
          },
        ).then(res => {
          if (res) {
            notification.success({
              message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
            });
            lineTableDs.query();
            return true;
          }
          notification.error({
            message: res?.message || res,
          });
          return false;
        });
      },
      cancel: () => {},
    });
  };

  const handleBatchFill = () => {
    // 获取当前编辑的列名及值，批量赋值给行ds的所有行该列
    const values = lineTableDs?.current?.get(currentColunm);
    if (values) {
      lineTableDs.selected.forEach(record => {
        if (record?.get('status') === 'NEW') {
          record.set(currentColunm, values);
        }
      });
    }
  };

  const handleCopyProductionOrder = async () => {
    if (!lineTableDs.selected.length) {
      return notification.error({
        message: intl
          .get(`${modelPrompt}.selectError`)
          .d('当前未选择单据行，请先选择行再执行操作!'),
      });
    }

    const copyProductionOrderDs = new DataSet({
      autoCreate: true,
      fields: [
        {
          name: 'volume',
          type: 'number',
          required: true,
          label: intl.get('tarzan.common.modal.label.volume').d('生产卷数'),
        },
        {
          name: 'material',
          type: 'object',
          label: intl.get(`${modelPrompt}.model.productionOrderMgt.materialId`).d('物料'),
          lovCode: 'MT.METHOD.MATERIAL',
          noCache: true,
          ignore: 'always',
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'materialId',
          bind: 'material.materialId',
        },
        {
          name: 'metersStandradRoll',
          type: 'number',
          label: intl.get(`${modelPrompt}.metersStandradRoll`).d('标准卷长'),
        },
      ],
    });

    Modal.open({
      title: intl.get(`${modelPrompt}.button.copy.product.order`).d('复制生产指令'),
      style: {
        width: 700,
      },
      children: (
        <>
          <NumberField
            dataSet={copyProductionOrderDs}
            name="volume"
            placeholder={intl.get('tarzan.common.modal.volume.').d('生产卷数')}
            required
            style={{ marginRight: '15px' }}
          />
          <Lov
            dataSet={copyProductionOrderDs}
            name="material"
            placeholder={intl.get(`${modelPrompt}.model.productionOrderMgt.materialId`).d('物料')}
            style={{ marginRight: '15px' }}
          />
          <NumberField
            dataSet={copyProductionOrderDs}
            name="metersStandradRoll"
            placeholder={intl.get(`${modelPrompt}.metersStandradRoll`).d('标准卷长')}
          />
        </>
      ),
      onOk: async () => {
        const validateResult = await copyProductionOrderDs.validate();
        if (!validateResult) {
          notification.error({
            message: intl.get('tarzan.common.message.error.validate').d('请检查输入数据'),
          });
          return false;
        }
        const data = {
          wo: lineTableDs?.selected[0].toData(),
          volume: copyProductionOrderDs.current.get('volume'),
          materialId: copyProductionOrderDs.current?.get('materialId'),
          metersStandradRoll: copyProductionOrderDs.current?.get('metersStandradRoll'),
        };
        return request(`${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/hme-make-order/line/copy/wo`, {
          method: 'POST',
          body: data,
        }).then(res => {
          if (res && res.success) {
            notification.success({
              message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
            });
            headerRowClick(headerDetail);
            setCopyProductDisabled(false);
          } else {
            notification.error({
              message: res?.message || res,
            });
            return false;
          }
        });
      },
      cancel: () => {},
    });
  };

  const lineTableButtons = [
    <Button
      color={ButtonColor.primary}
      key="calculation"
      disabled={
        !copyProductDisabled ||
        isEmpty(headerDetail) ||
        !(headerDetail?.toData()?.status === 'NEW' || headerDetail?.toData()?.alterFlag === 'Y') ||
        headerDetail?.toData()?.status === 'CLOSED'
      }
      onClick={handleCopyProductionOrder}
    >
      {intl.get(`${modelPrompt}.button.copy.product.order`).d('复制生产指令')}
    </Button>,
    <Button
      color={ButtonColor.primary}
      key="prodTargetValueChange"
      disabled={lineTableDs.selected.length !== 1 || isEmpty(headerDetail)}
      onClick={handleProdTargetValueChange}
    >
      {intl.get(`${modelPrompt}.button.prodTargetValueChange`).d('生产目标值变更')}
    </Button>,
    <Button
      color={ButtonColor.primary}
      key="calculation"
      disabled={
        !canEdit || lineTableDs.selected.length === 0 || headerDetail?.toData()?.status === 'CLOSED'
      }
      onClick={handleBatchFill}
    >
      {intl.get(`${modelPrompt}.button.batch.fill`).d('批量填充')}
    </Button>,
    <Lov
      name="workOderLov"
      dataSet={importDs}
      color={ButtonColor.primary}
      mode={ViewMode.button}
      clearButton={false}
      noCache
      disabled={
        isEmpty(headerDetail) ||
        !(headerDetail?.toData()?.status === 'NEW' || headerDetail?.toData()?.alterFlag === 'Y') ||
        headerDetail?.toData()?.status === 'CLOSED'
      }
      onChange={handleToImport}
    >
      {intl.get(`${modelPrompt}.production.instructions.import`).d('生产指令导入')}
    </Lov>,
    <Button
      color={ButtonColor.primary}
      key="calculation"
      disabled={
        isEmpty(headerDetail) ||
        !lineTableDs?.selected?.length ||
        headerDetail?.toData()?.status === 'CLOSED'
      }
      onClick={handleDemandCalculation}
    >
      {intl.get(`${modelPrompt}.button.material.demand.calculation`).d('辅料需求计算')}
    </Button>,
    canEdit ? (
      <>
        <Button
          color={ButtonColor.primary}
          icon="save"
          disabled={headerDetail?.toData()?.status === 'CLOSED'}
          onClick={() => {
            handleLineTableSave();
          }}
        >
          {intl.get('tarzan.common.button.save').d('保存')}
        </Button>
        <Button
          icon="close"
          disabled={headerDetail?.toData()?.status === 'CLOSED'}
          onClick={() => {
            setCanEdit(false);
            lineTableDs.reset();
          }}
        >
          {intl.get('tarzan.common.button.cancel').d('取消')}
        </Button>
      </>
    ) : (
      <Button
        color={ButtonColor.primary}
        icon="edit-o"
        disabled={
          isEmpty(headerDetail) ||
          !(
            headerDetail?.toData()?.status === 'NEW' ||
            headerDetail?.toData()?.alterFlag === 'Y' ||
            headerDetail?.toData()?.auditStatus === '1'
          ) ||
          headerDetail?.toData()?.status === 'CLOSED'
        }
        onClick={() => {
          setCanEdit(prev => !prev);
        }}
      >
        {intl.get('tarzan.common.button.edit').d('编辑')}
      </Button>
    ),
    <Button
      color={ButtonColor.primary}
      disabled={
        isEmpty(headerDetail) ||
        !(headerDetail?.toData()?.status === 'NEW' || headerDetail?.toData()?.alterFlag === 'Y') ||
        !closeDisabled ||
        headerDetail?.toData()?.status === 'CLOSED'
      }
      key="calculation"
      onClick={handleLineTableClose}
    >
      {intl.get(`${modelPrompt}.button.material.demand.calculation`).d('关闭行')}
    </Button>,
    <Button
      color={ButtonColor.primary}
      key="calculation"
      disabled={
        lineCloseData?.length === 0 || !closeDisabled || headerDetail?.toData()?.status === 'CLOSED'
      }
      onClick={handleLineTableCloseShow}
    >
      {intl.get(`${modelPrompt}.button.material.demand.calculation.show`).d('是否展示关闭行')}
    </Button>,
  ];

  const handleRollUp = () => {
    const makeOrderIdList = headerTableDs.selected.map(item => item.get('makeOrderId'));
    const headerStatus = headerTableDs.selected.map(item => item.get('status'))[0];
    const headerAuditStatus = headerTableDs.selected.map(item => item.get('auditStatus'))[0];
    const alterFlag = headerTableDs.selected.map(item => item.get('alterFlag'))[0];
    // 胶膜拼卷计划-拼卷
    props.history.push({
      pathname: `/hmes/glue-film-splicing-plan/roll-up`,
      state: {
        makeOrderIdList,
        headerStatus,
        alterFlag,
        headerAuditStatus,
      },
    });
  };

  const handleAntiAudit = () => {
    const data = headerTableDs?.selected?.map(item => item.toData()) || [];

    return request(
      `${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/hme-make-order/splicing/plan/counter/audit`,
      {
        method: 'POST',
        body: data,
      },
    ).then(res => {
      if (!res.success) {
        notification.error({
          message: res?.message,
        });
      } else {
        headerTableDs.batchUnSelect(headerTableDs.selected);
        headerTableDs.clearCachedSelected();
        setSelectedStatus([]);
        setSelectedCanChange([]);
        setSelectedAuditStatus([]);
        headerTableDs.query(props.headerTableDs.currentPage);
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      }
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.glueFilmSplicingPlan`).d('胶膜拼卷计划')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="close"
          onClick={clickMenu}
          disabled={
            !selectedCanChange.length ||
            (headerTableDs?.selected &&
              headerTableDs.selected.some(item => item.get('status') === 'CLOSED'))
          }
          permissionList={[
            {
              code: `button.change`,
              type: 'button',
              meaning: '列表页-变更按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.change`).d('变更')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="refresh"
          onClick={splicingApply}
          disabled={
            !selectedAuditStatus.length ||
            (headerTableDs?.selected &&
              headerTableDs.selected.some(item => item.get('status') === 'CLOSED'))
          }
          permissionList={[
            {
              code: `button.approve`,
              type: 'button',
              meaning: '列表页-拼卷计划审核按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.approve`).d('拼卷计划审核')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          onClick={handleRollUp}
          disabled={
            selectedStatus.length !== 1 ||
            (headerTableDs?.selected &&
              headerTableDs.selected.some(item => item.get('status') === 'CLOSED'))
          }
          permissionList={[
            {
              code: `${path}.button.splicing`,
              type: 'button',
              meaning: '列表页-拼卷按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.splicing`).d('拼卷')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreateWorkOrder}
          disabled={
            headerTableDs?.selected &&
            headerTableDs.selected.some(item => item.get('status') === 'CLOSED')
          }
          permissionList={[
            {
              code: `${path}.button.createWorkOrder`,
              type: 'button',
              meaning: '列表页-生产指令创建按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.createWorkOrder`).d('生产指令创建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          onClick={handleAntiAudit}
          disabled={
            selectedStatus.length !== 1 ||
            (headerTableDs?.selected &&
              headerTableDs.selected.some(item => item.get('status') === 'CLOSED'))
          }
          permissionList={[
            {
              code: `${path}.button.antiAudit`,
              type: 'button',
              meaning: '列表页-反审核按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.antiAudit`).d('反审核')}
        </PermissionButton>
        <TemplatePrintButton
          printButtonCode="HME.SPLICING_PLAN_PRINT"
          printParams={{
            planid: headerTableDs?.selected.map(item => item.get('makeOrderId')).join(','),
          }}
          disabled={false}
          icon=""
        />
      </Header>
      <Content>
        <Table
          searchCode="jmpjjh"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
            onFieldEnterDown,
          }}
          dataSet={headerTableDs}
          columns={headerTableColumns}
          customizable
          customizedCode="hearder"
          queryFieldsLimit={10}
          highLightRow
          onRow={({ record }) => {
            return {
              onClick: () => {
                headerRowClick(record);
              },
            };
          }}
        />
        <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="basicInfo"
            dataSet={lineTableDs}
          >
            <Table
              searchCode="line"
              dataSet={lineTableDs}
              columns={lineTableColumns}
              buttons={lineTableButtons}
              highLightRow
              queryBar={TableQueryBarType.none}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              customizable
              customizedCode="line"
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.glueFilmSplicingPlan', 'tarzan.common'] }),
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      const demandDetailDs = new DataSet({
        ...demandDetailDS(),
      });
      return {
        headerTableDs,
        lineTableDs,
        demandDetailDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
)(Order);
