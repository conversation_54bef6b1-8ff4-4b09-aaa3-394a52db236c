/**
 * @Description: 盘点工作台详情页-盘点明细Tab
 * @Author: <<EMAIL>>
 * @Date: 2022-02-14 13:31:26
 * @LastEditTime: 2023-01-10 11:00:39
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect } from 'react';
import { Table } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import styles from './index.module.less';

export default ({ ds, stocktakeStatus, setSelectedStocktakeBarcodeList }) => {
  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  // 生成行列表DS查询项
  const listener = flag => {
    // 列表交互监听
    if (ds) {
      const handler = flag ? ds.addEventListener : ds.removeEventListener;
      // 选中和撤销选中事件
      handler.call(ds, 'batchSelect', handleLocatorRangeTableChange);
      handler.call(ds, 'batchUnSelect', handleLocatorRangeTableChange);
    }
  };

  // 库位范围表格行选中事件
  const handleLocatorRangeTableChange = ({ dataSet }) => {
    const _selectedTableLines: number[] = [];
    dataSet.selected.forEach(item => {
      _selectedTableLines.push(item.toData());
    });
    setSelectedStocktakeBarcodeList(_selectedTableLines);
  };

  const getColumns = () => {
    const columns: ColumnProps[] = [
      {
        name: 'materialCode',
        width: 180,
      },
      {
        name: 'materialName',
        minWidth: 180,
      },
      {
        name: 'revisionCode',
        width: 180,
      },
      {
        name: 'identifyTypeDesc',
        width: 180,
      },
      {
        name: 'locatorCode',
        width: 180,
      },
      {
        name: 'locatorName',
        minWidth: 180,
      },
      {
        name: 'sumQuantity',
        width: 100,
      },
    ];
    const dynamicColumns: ColumnProps[] = [
      {
        name: 'adjustQty',
        width: 100,
      },
      {
        name: 'firstCountQty',
        width: 100,
      },
      {
        name: 'firstCountDiffQty',
        width: 100,
        renderer: ({ value }) => {
          return (
            value && <Tag color={value < 0 ? 'red' : 'green'}>{value}</Tag>
          );
        },
      },
      {
        name: 'reCountQty',
        width: 100,
      },
      {
        name: 'reCountDiffQty',
        width: 100,
        renderer: ({ value }) => {
          return (
            value && <Tag color={value < 0 ? 'red' : 'green'}>{value}</Tag>
          );
        },
      },
    ];
    const endColumns: ColumnProps[] = [
      {
        name: 'uomCode',
        width: 100,
      },
    ];
    if (['NEW', 'RELEASED'].includes(stocktakeStatus)) {
      return columns.concat(endColumns);
    }
    return columns.concat(dynamicColumns).concat(endColumns);
  };

  return (
    <div className={styles['card-table']}>
      <Table
        className={styles['expand-table']}
        queryBar={TableQueryBarType.bar}
        dataSet={ds}
        columns={getColumns()}
        pagination={{ pageSizeOptions: ['10', '20', '50', '100', '500', '1000', '2000'] }}
      />
    </div>
  );
};
