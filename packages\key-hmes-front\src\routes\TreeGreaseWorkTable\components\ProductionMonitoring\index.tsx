/* eslint-disable jsx-a11y/alt-text */
// 加工件（工单+在制品标识）
/*
 * @workOrder 树脂粉工作台-员工上离岗
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-10-11 14:32:39
 * @copyright Copyright (c) 2024 , Hand
 */

import React, { useEffect, useMemo, useState, useRef } from 'react';
import { DataSet, Table, Modal, Button, Select } from 'choerodon-ui/pro';
import { Radio, Badge } from 'choerodon-ui';
import moment from 'moment';
import intl from 'utils/intl';
import arrowCircle from '@/assets/icons/arrow_circle.png';
import formatterCollections from 'utils/intl/formatterCollections';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { feedingPlanSheetDS, dataRecordDS, prodBatchListDS } from './stores';
import { CardLayout, useRequest, ONotification } from '../commonComponents';
import { useOperationPlatform } from '../../contextsStore';
import { EoRecordInfo, SubmitInfo, QueryRecordDataInfo, ProdBatchCreate } from './services';
import styles from './index.modules.less';
import TemplatePrintButton from '../../TemplatePrintButton';
import DataRecord from './dataRecord';

const modelPrompt = 'tarzan.productionMonitoring';

interface GetHeaderContent {
  onChange: any;
  active: string;
}
const GetHeaderContent: React.FC<GetHeaderContent> = ({ onChange, active }) => {
  return (
    <div className={styles.getHeaderContent}>
      <Radio.Group value={active} onChange={onChange}>
        <Radio.Button value="WORKING">待处理</Radio.Button>
        <Radio.Button value="COMPLETED">已完成</Radio.Button>
        <Radio.Button value="ABANDON">已报废</Radio.Button>
      </Radio.Group>
    </div>
  );
};

const ProductionMonitoring = props => {
  let dataRecordModal;
  const { cardId } = props;
  const { enterInfo, dispatch } = useOperationPlatform();
  // @ts-ignore
  const { shiftDate, shiftCode, prodLineId, shiftTeamName, shiftTeamId } = enterInfo;

  console.log('enterInfo', enterInfo);
  

  const { run: eoRecordInfo, loading: eoRecordLoading } = useRequest(EoRecordInfo(), {
    manual: true,
    needPromise: true,
  });
  const { run: submitInfo } = useRequest(SubmitInfo(), { manual: true, needPromise: true });
  const { run: queryRecordDataInfo } = useRequest(QueryRecordDataInfo(), {
    manual: true,
    needPromise: true,
  });
  const { run: prodBatchCreate } = useRequest(ProdBatchCreate(), {
    manual: true,
    needPromise: true,
  });

  const [activeBtn, setActiveBtn] = useState('WORKING');
  const [background, setBackground] = useState('');
  const [workOrderId, setWorkOrderId] = useState('');
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>([]);
  const [onfresh, setOnfresh] = useState(false);
  const print = useRef<any>(null);
  const [printParams, setPrintParams] = useState<any>({});
  // 当前日期
  const [currentDate, setCurrentDate] = useState(moment(shiftDate).format('YYYY-MM-DD'));
  const feedingPlanSheetDs = useMemo(
    () =>
      new DataSet({
        ...feedingPlanSheetDS(),
      }),
    [],
  );
  const prodBatchListDs = useMemo(
    () =>
      new DataSet({
        ...prodBatchListDS(),
      }),
    [],
  );

  const dataRecordDs = useMemo(
    () =>
      new DataSet({
        ...dataRecordDS(),
      }),
    [],
  );

  useEffect(() => {
    handleToQuery();
  }, [cardId, onfresh]);

  useEffect(() => {
    if (workOrderId) {
      queryDataList();
    }
  }, [activeBtn, workOrderId, onfresh]);

  const queryDataList = (type = null) => {
    // const statusList = activeBtn === 'WORKING' ? [activeBtn, 'WORKING'] : [activeBtn]
    eoRecordInfo({
      params: {
        prodLineId,
        workOrderId,
        eoStatus: activeBtn,
      },
    }).then(res => {
      if (res?.success) {
        const { eoResultList, routerStepResultList } = res?.rows;
        const data =
          routerStepResultList?.map(item => {
            const eoList = (
              eoResultList?.filter(obj => item?.routerStepId === obj?.routerStepId) || []
            ).map(obj => ({
              ...obj,
            }));
            return {
              ...res?.rows,
              ...item,
              processList: eoList,
              isHandleType: type === 'handleType',
            };
          }) || [];
        data.forEach(item => {
          if (item.routerStepDesc === "树脂粉-水洗" ) {
            item.processList = sortByGradeAndDate(item?.processList)
          }
        });
        if (activeBtn === 'WORKING') {
          setBackground('green');
        } else if (activeBtn === 'COMPLETED') {
          setBackground('grey');
        } else {
          setBackground('red');
        }
        setData(data || []);
      }
    });
  };

  const sortByGradeAndDate = (data) => {
    // 定义优先级顺序
    const priorityOrder = {
      "三级水洗": 1,
      "二级水洗": 2,
      "一级水洗": 3,
      "null": 4,
    };
  
    return [...data].sort((a, b) => {
      // 处理null值
      const gradeA = a.workStationGrade || "null";
      const gradeB = b.workStationGrade || "null";
      
      // 比较优先级
      const priorityA = priorityOrder[gradeA] || 5;
      const priorityB = priorityOrder[gradeB] || 5;
      
      // 优先级不同时按优先级排序
      if (priorityA !== priorityB) {
        return priorityA - priorityB;
      }
      
      // 优先级相同时按wipDate降序排序（最新日期在前）
      return new Date(b.wipDate) - new Date(a.wipDate);
    });
  }

  const handleToQuery = (date = null) => {
    setWorkOrderId('');
    feedingPlanSheetDs.setQueryParameter('prodLineId', prodLineId);
    feedingPlanSheetDs.setQueryParameter('shiftDate', date || enterInfo.shiftDate);
    feedingPlanSheetDs.query().then(res => {
      // 改取第一条的
      if (res?.success) {
        if (res?.rows?.length > 0) {
          setWorkOrderId(res?.rows[0]?.workOrderId);
          dispatch({
            // @ts-ignore
            type: 'update',
            payload: {
              enterInfo: {
                ...enterInfo,
                workOrderId: res?.rows[0]?.workOrderId,
              },
            },
          });
        }
      } else {
        feedingPlanSheetDs.loadData([]);
        setData([]);
        ONotification.error({
          message: res?.message,
          description: undefined,
        });
      }
    });
  };

  const feedingPlanColumns = [
    { name: 'workOrderNum' },
    { name: 'prodLineCode' },
    { name: 'prodLineName' },
    { name: 'materialCode' },
    { name: 'materialName' },
    { name: 'qty' },
    { name: 'completedQty' },
  ];

  const prodBatchListColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        title: '序号',
        width: 90,
        align: 'center',
        renderer: ({ record }) => {
          const index: number = (record?.index || 0) + 1;
          return index > 9 ? index : `0${index}`;
        },
      },
      { name: 'identification' },
      {
        name: 'workcellId',
        editor: () => {
          return <Select name="workcellId" />;
        },
      },
      { name: 'date' },
      {
        header: '操作',
        align: 'center',
        renderer: ({ record }) => (
          <Button
            color={record?.get('workcellId') ? ButtonColor.primary : ButtonColor.gray}
            disabled={!record?.get('workcellId')}
            onClick={() => {
              if (record?.get('workcellId')) {
                handleBatchCreate(record);
              }
            }}
          >
            批次创建
          </Button>
        ),
      },
    ];
  }, []);

  const handleBatchCreate = async record => {
    if (!record.get('workcellId')) {
      ONotification.warning({
        message: '请选择工位！',
        description: undefined,
      });
      return;
    }
    const params = {
      routerStepId: record.get('routerStepId'),
      identification: record.get('identification'),
      workcellId: record.get('workcellId'),
      workOrderId: prodBatchListDs?.getState('workOrderId'),
    };
    setLoading(true);
    const res = await prodBatchCreate({
      params,
    });
    if (res?.success) {
      prodBatchListDs.query();
      ONotification.success({
        message: '操作成功！',
        description: undefined,
      });
    } else {
      ONotification.error({
        message: res.message,
        description: undefined,
      });
    }
    setLoading(false);
  };

  const handleChange = val => {
    setActiveBtn(val.target.value);
  };

  const handleOpenModal = async (obj, name, item) => {
    // if (activeBtn === 'scrapped') {
    //   return;
    // }
    const params = {
      shiftTeamName,
      workOrderId,
      workcellId: obj.workcellId,
      eoId: obj.eoId,
    };
    setLoading(true);
    const res = await queryRecordDataInfo({
      params,
    });
    if (res?.success) {
      const { tagGroupList = [] } = res?.rows;
      if (tagGroupList?.length && tagGroupList[0]?.tagList?.length) {
        dataRecordDs.loadData(tagGroupList[0]?.tagList);
      } else {
        dataRecordDs.loadData([]);
      }
      setLoading(false);
    } else {
      dataRecordDs.loadData([]);
      setLoading(false);
    }
    const routerStepId = res?.rows?.routerStepId;
    dataRecordModal = Modal.open({
      key: Modal.key(),
      destroyOnClose: true,
      style: {
        width: '100%',
      },
      // fullScreen: true,
      className: styles.dataRecordModal,
      okText: ['COMPLETED', 'ABANDON'].includes(activeBtn)
        ? intl.get(`${modelPrompt}.close`).d('关闭')
        : intl.get(`${modelPrompt}.submit`).d('转交至下工序'),
      children: (
        <DataRecord
          data={{
            ...obj,
            ...item,
          }}
          dataList={res?.rows}
          title={
            <div>
              {`${name}数据记录`}
              <span
                style={{
                  fontSize: '22px',
                  marginLeft: '16px',
                  background: '#38708f6b',
                  padding: '4px',
                  border: '1px solid #2a63829e',
                }}
              >
                {obj.eoNum}
              </span>
              <span
                style={{
                  fontSize: '22px',
                  marginLeft: '16px',
                  background: '#38708f6b',
                  padding: '4px',
                  border: '1px solid #2a63829e',
                }}
              >
                {obj.workcellName}
              </span>
            </div>
          }
          dataSet={dataRecordDs}
          enterInfo={enterInfo}
          modal={dataRecordModal}
          queryDataList={queryDataList}
          params={params}
          queryRecordDataInfo={queryRecordDataInfo}
          yellowChangeInspect={res?.rows?.yellowChangeInspect}
          conductivityInspect={res?.rows?.conductivityInspect}
          activeBtn={activeBtn}
          workOrderId={workOrderId}
        />
      ),
      onOk: () =>
        handleSubmit({ ...obj, ...item, routerStepId, lastStepFlag: res?.rows?.lastStepFlag }),
      footer: (okBtn, cancelBtn) => {
        return (
          <>
            {cancelBtn}
            {!['COMPLETED', 'ABANDON'].includes(activeBtn) && okBtn}
          </>
        );
      },
    });
  };

  const handleSubmit = async data => {
    const validate = await dataRecordDs.validate();
    if (!validate) {
      return false;
    }
    Modal.confirm({
      key: Modal.key(),
      contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
      className: styles.machinedPartModal,
      children: <span style={{ color: 'white' }}>确认转交至下工序?</span>,
      onOk: () => handleSubmitRequest(data),
    });
  };

  const handleSubmitRequest = data => {
    submitInfo({
      params: {
        prodLineId,
        shiftTeamId,
        shiftDate,
        shiftCode,
        eoId: data.eoId,
        operationId: data.operationId,
        workcellId: data.workcellId,
        routerStepId: data.routerStepId,
      },
    })
      .then(res => {
        if (res?.success) {
          setPrintParams({
            eoId: data.eoId,
            shiftTeamId,
          });
          dataRecordModal.close(true);
          queryDataList();
          return true;
        }
        ONotification.error({ message: res?.message });
        return false;
      })
      .then(res => {
        if (res) {
          if (data?.lastStepFlag === 'DONE_STEP') {
            print.current?.print();
          }
        }
      });
  };

  const handleShowList = item => {
    prodBatchListDs.setQueryParameter('routerStepId', item.routerStepId);
    prodBatchListDs.setQueryParameter('prodLineId', item.prodLineId);
    prodBatchListDs.setState('routerStepId', item.routerStepId);
    prodBatchListDs.setState('prodLineId', prodLineId);
    prodBatchListDs.setState('workOrderId', workOrderId);
    prodBatchListDs.query();
    Modal.open({
      title: '生产批次列表',
      destroyOnClose: true,
      closable: true,
      style: {
        width: 1000,
      },
      children: (
        <Table
          dataSet={prodBatchListDs}
          columns={prodBatchListColumns}
          style={{ maxHeight: 300, overflow: 'auto' }}
          className={styles.batchList}
        />
      ),
      footer: null,
      onClose: () => queryDataList(),
    });
  };

  const handleTimeChange = async type => {
    let shiftDates;
    if (type === 'back') {
      shiftDates = moment(currentDate)
        .subtract(1, 'days')
        .format('YYYY-MM-DD');
    } else {
      shiftDates = moment(currentDate)
        .add(1, 'days')
        .format('YYYY-MM-DD');
    }
    setCurrentDate(shiftDates);
    handleToQuery(shiftDates);
  };

  // 检测报告单表格行点击
  const clickHeaderRow = record => {
    if (record?.get('workOrderId')) {
      const workOrderId = record?.get('workOrderId');
      setWorkOrderId(workOrderId);
      dispatch({
        // @ts-ignore
        type: 'update',
        payload: {
          enterInfo: {
            ...enterInfo,
            workOrderId,
          },
        },
      });
    }
  };

  const getBackgroundColorByGrade = grade => {
    switch (grade) {
      case '一级水洗':
        return '#1a9c49'; // 绿色
      case '二级水洗':
        return '#2366a8'; // 深蓝色
      case '三级水洗':
        return '#d48806'; // 深橙色
      default:
        return background;
    }
  };

  const getBorderColorByGrade = grade => {
    switch (grade) {
      case '一级水洗':
        return '#22c97a';
      case '二级水洗':
        return '#3a8fff';
      case '三级水洗':
        return '#ffb84d';
      default:
        return '#cccccc';
    }
  };

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <CardLayout.Layout spinning={eoRecordLoading} className={styles.todayTask}>
        <CardLayout.Header
          title={intl.get(`${modelPrompt}.todayTask`).d('今日投料计划单/任务')}
          addonAfter={
            <div className={styles.headerAfter}>
              <div onClick={() => handleTimeChange('back')}>
                <img src={arrowCircle} alt="" className={styles.iconSize} />
              </div>
              <div>{currentDate}</div>
              <div onClick={() => handleTimeChange('forward')}>
                <img src={arrowCircle} alt="" className={styles.iconSize2} />
              </div>
              <div className={styles.actionIconDiv}>
                <Button
                  color={ButtonColor.primary}
                  onClick={() => {
                    setOnfresh(!onfresh);
                    setCurrentDate(moment(shiftDate).format('YYYY-MM-DD'));
                  }}
                >
                  刷新
                </Button>
              </div>
            </div>
          }
        />
        <CardLayout.Content className="todayTaskContent">
          <div>
            <Table
              dataSet={feedingPlanSheetDs}
              columns={feedingPlanColumns}
              onRow={({ record }) => {
                return {
                  onClick: () => {
                    clickHeaderRow(record);
                  },
                };
              }}
            />
          </div>
        </CardLayout.Content>
      </CardLayout.Layout>
      <CardLayout.Layout spinning={eoRecordLoading || loading} className={styles.shopOperationTask}>
        <CardLayout.Header
          title={intl.get(`${modelPrompt}.shopOperationTask`).d('车间操作任务')}
          content={<GetHeaderContent onChange={handleChange} active={activeBtn} />}
        />
        <CardLayout.Content className={styles.shopOperationTaskContent}>
          <div className={styles.cardContent}>
            {data.map(item => (
              <div
                className={styles.outerBox}
                style={{ width: `calc(100% / ${data.length} - 20px)` }}
              >
                <div className={styles.processContent}>
                  <span>{item.routerStepDesc}</span>
                  <a
                    onClick={() => {
                      handleShowList(item);
                    }}
                  >
                    +
                  </a>
                </div>
                <div style={{ width: '100%', height: 'calc(100% - 30px)', overflow: 'auto' }}>
                  {item.processList.map(obj => (
                    <div
                      className={styles.productionBatchBody}
                      style={{
                        backgroundColor: getBackgroundColorByGrade(obj.workStationGrade),
                        position: 'relative',
                      }}
                      onClick={() => handleOpenModal(obj, item.routerStepDesc, item)}
                    >
                      <div className={styles.productionBatchCode}>
                        <i>{obj.eoNum}</i>
                      </div>
                      <div className={styles.productionBatchName}>
                        <p>
                          {obj.workcellName}
                          <Badge
                            status={
                              obj.experimentalBatchFlag === 'Y' || obj.experimentalBatchFlag === 'T'
                                ? 'error'
                                : undefined
                            }
                            text={
                              obj.experimentalBatchFlag === 'Y'
                                ? intl.get(`${modelPrompt}.experimentalBatch`).d('试验')
                                : obj.experimentalBatchFlag === 'T'
                                ? intl.get(`${modelPrompt}.specialBatch`).d('特殊')
                                : null
                            }
                            className={styles.eoTag}
                          />
                        </p>
                        <p>{obj.wipDate}</p>
                        {obj.workStationGrade && (
                          <div
                            className={styles.extAttrCircle}
                            style={{
                              backgroundColor: 'transparent',
                              border: `2px solid ${getBorderColorByGrade(obj.workStationGrade)}`,
                            }}
                          >
                            {obj.workStationGrade}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )) || null}
          </div>
          <div style={{ display: 'none' }}>
            <TemplatePrintButton
              ref={print}
              printButtonCode="HME.PRODUCT_INSPECT_DONE"
              name={intl.get(`${modelPrompt}.print`).d('打印')}
              printParams={printParams}
              icon=""
              disabled={false}
            />
          </div>
        </CardLayout.Content>
      </CardLayout.Layout>
    </div>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(ProductionMonitoring);
