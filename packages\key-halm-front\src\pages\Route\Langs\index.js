import intl from 'utils/intl';
import getCommonLangs from 'alm/langs';

const getLangs = key => {
  const PREFIX = 'aori.route';
  const MODELPREFIX = `${PREFIX}.model.route`;
  const LANGS = {
    ...getCommonLangs(),
    // title
    TITLE: intl.get(`${PREFIX}.view.message.title`).d('路线'),
    TITLE_POINT: intl.get(`${PREFIX}.view.message.point`).d('点位'),
    // button
    REMOVE: intl.get(`${PREFIX}.view.message.button.remove`).d('移除'),

    // message
    DIS_ROUTE: intl
      .get(`${PREFIX}.view.message.disableRoute`)
      .d('路线已禁用，启用路线至少需关联一条点位信息'),

    // model
    ROUTE_NAME: intl.get(`${MODELPREFIX}.routeName`).d('路线名称'),
    ROUTE_CODE: intl.get(`${MODELPREFIX}.routeCode`).d('路线代码'),
    POINT: intl.get(`${MODELPREFIX}.point`).d('点位'),
    ROUTE_DESC: intl.get(`${MODELPREFIX}.description`).d('路线说明'),
    ORDER_FLAG: intl.get(`${MODELPREFIX}.orderFlag`).d('顺序路线'),
    MAINTSITE: intl.get(`${MODELPREFIX}.maintSite`).d('服务区域'),
    POINT_SEQ_NUM: intl.get(`${MODELPREFIX}.seqNum`).d('序号'),
    POINT_NAME: intl.get(`${MODELPREFIX}.pointName`).d('点位名称'),
    POINT_CODE: intl.get(`${MODELPREFIX}.pointCode`).d('点位代码'),
    POINT_TYPE: intl.get(`${MODELPREFIX}.pointType`).d('点位类型'),
    POINT_OBJ: intl.get(`${MODELPREFIX}.pointObjectName`).d('点位对象'),
    POINT_STATUS: intl.get(`${MODELPREFIX}.pointStatus`).d('点位状态'),
    SORT: intl.get(`${MODELPREFIX}.sort`).d('排序'),

    // panel
    PANEL_BASE_INFO: intl.get(`${PREFIX}.view.message.panel.baseInfo`).d('基础信息'),
    PANEL_POINTS: intl.get(`${PREFIX}.view.message.panel.points`).d('路线点'),
  };
  return LANGS[key];
};

export default getLangs;
