import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'filmDeliveryReport';
const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();

const inspectionReportDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.single,
  queryFields: [
    {
      name: 'createDateStart',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.createDateStart`).d('日期从'),
      max: 'createDateEnd',
    },
    {
      name: 'createDateEnd',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.createDateEnd`).d('日期至'),
      min: 'createDateStart',
    },
    {
      name: 'corporationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.corporation`).d('客户'),
      lovCode: 'MT.CUSTOMER.APS',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'corporationId',
      type: FieldType.number,
      bind: 'corporationLov.customerId',
    },
    {
      name: 'reportType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reportType`).d('报告类型'),
    },
  ],
  fields: [
    {
      name: 'reportCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reportCode`).d('报告单号'),
    },
    {
      name: 'reportType',
      type: FieldType.string,
      required: true,
      lookupCode: 'JM_OUT_FACTORY_REPORT_PRINT_PROVE',
      label: intl.get(`${modelPrompt}.reportType`).d('报告类型'),
      valueField: 'printTemplateCode',
      textField: 'printTemplateName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'reportStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reportStatus`).d('报告单状态'),
      lookupCode: 'JM_OUT_FACTORY_REPORT_STATE',
      defaultValue: 'NEW',
    },
    {
      name: 'enforceStandard',
      type: FieldType.string,
      // required: true,
      label: intl.get(`${modelPrompt}.enforceStandard`).d('执行标准'),
      lookupCode: 'QMS.FILM_REPORT_ZXBZ',
      textField: 'meaning',
      valueField: 'meaning',
    },
    {
      name: 'corporationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.corporationName`).d('公司名称'),
      lovCode: 'DST.REPORT.CUSTOMER',
      textField: 'customerNameAndCode',
      // required: true,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'corporationId',
      type: FieldType.number,
      bind: 'corporationLov.customerId',
    },
    {
      name: 'corporationName',
      type: FieldType.string,
      bind: 'corporationLov.customerName',
    },
    {
      name: 'corporationCode',
      type: FieldType.string,
      bind: 'corporationLov.customerCode',
    },
    {
      name: 'customerNameAndCode',
      type: FieldType.string,
      bind: 'corporationLov.customerNameAndCode',
    },
    {
      name: 'telephone',
      type: FieldType.string,
      bind: 'corporationLov.phone',
      label: intl.get(`${modelPrompt}.phone`).d('电话'),
    },
    {
      name: 'corporationAddress',
      type: FieldType.string,
      bind: 'corporationLov.address',
      label: intl.get(`${modelPrompt}.companyAddress`).d('公司地址'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      // lookupCode: 'QMS.FILM_REPORT_BZ',
      // textField: 'meaning',
      // valueField: 'meaning',
    },
    {
      name: 'createdBy',
      type: FieldType.string,
      defaultValue: userInfo.id,
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
      defaultValue: userInfo.realName,
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      defaultValue: new Date(),
    },
    {
      name: 'auditUser',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.auditUser`).d('审核人'),
    },
    {
      name: 'auditDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.auditDate`).d('审核时间'),
    },
    {
      name: 'docDate',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.docDate`).d('单据日期'),
      format: 'YYYY-MM-DD',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-film-reports/list/ui`,
        method: 'GET',
      };
    },
  },
});

const volumeNumDetailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  paging: false,
  fields: [
    {
      name: 'volNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.volNum`).d('销售卷号'),
    },
    {
      name: 'volNumLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.volNumLot`).d('生产卷号'),
    },
    {
      name: 'color',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.color`).d('颜色'),
    },
    {
      name: 'productionBatches',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionBatches`).d('生产批次'),
    },
    {
      name: 'productType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productType`).d('产品类型'),
    },
    {
      name: 'widthOfCloth',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.widthOfCloth`).d('幅宽/mm'),
    },
    {
      name: 'colorWidth',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.colorWidth`).d('色带宽度'),
    },
    {
      name: 'materialAngle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialAngle`).d('物料角度'),
    },
    {
      name: 'length',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.length`).d('长度/m'),
      precision: 0,
    },
    {
      name: 'thickness',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.thickness`).d('厚度/mm'),
      precision: 2,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-film-report-reelnos/list/ui`,
        method: 'GET',
      };
    },
  },
});

const inspectionDetailDS: () => DataSetProps = () => ({
  selection: false,
  autoCreate: false,
  autoQuery: false,
  paging: false,
  fields: [
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('检验单号'),
    },
    {
      name: 'inspectItemValue',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.inspectItemValue`).d('检验项目'),
    },
    {
      name: 'inspectStandard',
      type: FieldType.string,
      // required: true,
      label: intl.get(`${modelPrompt}.inspectStandard`).d('检验标准'),
    },
    {
      name: 'inspectResult',
      type: FieldType.string,
      // required: true,
      label: intl.get(`${modelPrompt}.inspectResult`).d('检验结果'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-film-report-fqcs/list/ui`,
        method: 'GET',
      };
    },
  },
});

const selectDS: () => DataSetProps = () => ({
  selection: DataSetSelection.single,
  autoCreate: true,
  autoQuery: false,
  paging: false,
  fields: [
    {
      name: 'selectVolumeNum',
      type: FieldType.object,
      lovCode: 'JM_OUT_FACTORY_REPORT_MATERIAL_LOT',
      lovPara: {
        tenantId,
      },
      // multiple: true,
      ignore: FieldIgnore.always,
      multiple: true,
    },
  ],
});

const inspectionUpdateDS: () => DataSetProps = () => ({
  autoCreate: false,
  autoQuery: false,
  paging: false,
  fields: [
    {
      name: 'volNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.volNum`).d('销售卷号'),
    },
    {
      name: 'volNumLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.volNumLot`).d('生产卷号'),
    },
    {
      name: 'thicknes',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.thicknes`).d('目标值'),
      precision: 2,
    },
    {
      name: 'widthOfCloth',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.widthOfCloth`).d('宽幅'),
    },
    {
      name: 'materialAngle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialAngle`).d('物料角度'),
    },
    {
      name: 'color',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.color`).d('颜色'),
    },
  ],
});
export { inspectionReportDS, volumeNumDetailDS, inspectionDetailDS, selectDS, inspectionUpdateDS };
