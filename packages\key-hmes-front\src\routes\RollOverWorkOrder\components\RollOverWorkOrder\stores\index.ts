/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-17 11:31:28
 * @LastEditTime: 2023-07-20 10:37:38
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import moment from 'moment';

// BASIC.HMES_BASIC = "dst-mes-29210"
const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();
const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  // selection: DataSetSelection.single,
  paging: false,
  dataKey: 'rows',
  queryFields: [
    {
      name: 'startTime',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.startTime`).d('日期从'),
      required: true,
      max: 'endTime',
      defaultValue: moment(new Date()).format('YYYY-MM-DD 00:00:00'),
      transformRequest: value => {
        return value ? moment(value).format('YYYY-MM-DD 00:00:00') : value;
      },
    },
    {
      name: 'endTime',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.endTime`).d('至'),
      min: 'startTime',
      required: true,
      defaultValue: moment(new Date()).format('YYYY-MM-DD 23:59:59'),
      transformRequest: value => {
        return value ? moment(value).format('YYYY-MM-DD 23:59:59') : value;
      },
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('卷号'),
      transformRequest: () => undefined, // 阻止卷号字段发送到后端
    },
  ],
  fields: [
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令单'),
    },
    {
      name: 'oldEoNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.oldEoNumber`).d('原卷号'),
    },
    {
      name: 'workOrderStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderStatusDesc`).d('状态'),
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planStartTime`).d('日期'),
    },
    {
      name: 'oldProdModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.oldProdModel`).d('原产品规格'),
    },
    {
      name: 'newProdModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.newProdModel`).d('新产品规格'),
    },
    {
      name: 'prodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodDesc`).d('产线'),
    },
    {
      name: 'prodTargetValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodTargetValue`).d('目标厚度'),
    },
    {
      name: 'color',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.color`).d('颜色'),
    },
    {
      name: 'oldVolumeMeter',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.oldVolumeMeter`).d('原卷米数'),
    },
    {
      name: 'newVolumeMeter',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.newVolumeMeter`).d('新卷米数'),
    },
    {
      name: 'newEoNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.newEoNumber`).d('新卷号'),
    },
    {
      name: 'progressFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.progressFlag`).d('进度标识'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/roll-work-order-card/select/list`,
        method: 'POST',
      };
    },
  },
});
const FormDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'oldProdModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.oldProdModel`).d('产品规格'),
      disabled: true,
    },
    {
      name: 'oldEoNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.oldEoNumber`).d('原卷号'),
      disabled: true,
    },
    {
      name: 'newEoNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.newEoNumber`).d('新卷号'),
      disabled: true,
    },
    {
      name: 'planWeight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.planWeight`).d('计划重量'),
      disabled: true,
    },
    {
      name: 'actualWeight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.actualWeight`).d('实际重量'),
      required: true,
      precision: 3,
    },
  ],
});

const oldEoNumberFormDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'impurityOne',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.impurityOne`).d('杂质1'),
      disabled: true,
    },
    {
      name: 'impurityTwo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.impurityTwo`).d('杂质2'),
      disabled: true,
    },
    {
      name: 'impurityThree',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.impurityThree`).d('杂质3'),
      disabled: true,
    },
    {
      name: 'impurityFour',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.impurityFour`).d('杂质4'),
      disabled: true,
    },
    {
      name: 'joinOne',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.joinOne`).d('接头1'),
      disabled: true,
    },
    {
      name: 'joinTwo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.joinTwo`).d('接头2'),
      disabled: true,
    },
  ],
});

const newEoNumberFormDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'oldImpurityOne',
      type: FieldType.string,
    },
    {
      name: 'oldImpurityTwo',
      type: FieldType.string,
    },
    {
      name: 'oldImpurityThree',
      type: FieldType.string,
    },
    {
      name: 'oldImpurityFour',
      type: FieldType.string,
    },
    {
      name: 'impurityOne',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.impurityOne`).d('杂质1'),
      validator: (value, _name, record) => {
        if (value === undefined || value === null || value === '' || value === '-') return true;
        const parseRange = (val: string): [number, number] | null => {
          if (val.includes('-')) {
            const [minStr, maxStr] = val.split('-');
            const min = Number(minStr);
            const max = Number(maxStr);
            if (Number.isNaN(min) || Number.isNaN(max)) return null;
            if (!(min <= max)) return null;
            return [min, max];
          }
          const n = Number(val);
          if (Number.isNaN(n)) return null;
          return [n, n];
        };

        const current = parseRange(String(value));
        if (!current) return '请输入数字或范围，如 12 或 12-34';

        // 对比下一项（如已填写），当前最大值必须小于下一项最小值
        const nextRaw = (record as any)?.get?.('impurityTwo');
        if (nextRaw && nextRaw !== '-') {
          const next = parseRange(String(nextRaw));
          if (next && !(current[1] < next[0])) return '数据最大值必须小于后一个的最小值';
        }

        // 对比旧值（如存在），当前最小值必须大于旧值
        const oldRaw = (record as any)?.get?.('oldImpurityOne');
        if (oldRaw && oldRaw !== '-') {
          const oldNum = Number(oldRaw);
          if (!Number.isNaN(oldNum) && !(current[0] > oldNum)) return '新杂质必须大于前面的杂质';
        }
        return true;
      },
    },
    {
      name: 'impurityTwo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.impurityTwo`).d('杂质2'),
      validator: (value, _name, record) => {
        if (value === undefined || value === null || value === '' || value === '-') return true;
        const parseRange = (val: string): [number, number] | null => {
          if (val.includes('-')) {
            const [minStr, maxStr] = val.split('-');
            const min = Number(minStr);
            const max = Number(maxStr);
            if (Number.isNaN(min) || Number.isNaN(max)) return null;
            if (!(min <= max)) return null;
            return [min, max];
          }
          const n = Number(val);
          if (Number.isNaN(n)) return null;
          return [n, n];
        };
        const current = parseRange(String(value));
        if (!current) return '请输入数字或范围，如 12 或 12-34';

        // 与前一个比较：当前最小值必须大于前一个最大值
        const prevRaw = (record as any)?.get?.('impurityOne');
        if (prevRaw && prevRaw !== '-') {
          const prev = parseRange(String(prevRaw));
          if (prev && !(current[0] > prev[1])) return '数据最小值必须大于前一个的最大值';
        }

        // 与后一个比较：当前最大值必须小于后一个最小值
        const nextRaw = (record as any)?.get?.('impurityThree');
        if (nextRaw && nextRaw !== '-') {
          const next = parseRange(String(nextRaw));
          if (next && !(current[1] < next[0])) return '数据最大值必须小于后一个的最小值';
        }

        // 对比旧值（如存在），当前最小值必须大于旧值
        const oldRaw = (record as any)?.get?.('oldImpurityTwo');
        if (oldRaw && oldRaw !== '-') {
          const oldNum = Number(oldRaw);
          if (!Number.isNaN(oldNum) && !(current[0] > oldNum)) return '新杂质必须大于前面的杂质';
        }
        return true;
      },
    },
    {
      name: 'impurityThree',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.impurityThree`).d('杂质3'),
      validator: (value, _name, record) => {
        if (value === undefined || value === null || value === '' || value === '-') return true;
        const parseRange = (val: string): [number, number] | null => {
          if (val.includes('-')) {
            const [minStr, maxStr] = val.split('-');
            const min = Number(minStr);
            const max = Number(maxStr);
            if (Number.isNaN(min) || Number.isNaN(max)) return null;
            if (!(min <= max)) return null;
            return [min, max];
          }
          const n = Number(val);
          if (Number.isNaN(n)) return null;
          return [n, n];
        };
        const current = parseRange(String(value));
        if (!current) return '请输入数字或范围，如 12 或 12-34';

        // 前一项比较
        const prevRaw = (record as any)?.get?.('impurityTwo');
        if (prevRaw && prevRaw !== '-') {
          const prev = parseRange(String(prevRaw));
          if (prev && !(current[0] > prev[1])) return '数据最小值必须大于前一个的最大值';
        }

        // 后一项比较
        const nextRaw = (record as any)?.get?.('impurityFour');
        if (nextRaw && nextRaw !== '-') {
          const next = parseRange(String(nextRaw));
          if (next && !(current[1] < next[0])) return '数据最大值必须小于后一个的最小值';
        }

        // 旧值比较
        const oldRaw = (record as any)?.get?.('oldImpurityThree');
        if (oldRaw && oldRaw !== '-') {
          const oldNum = Number(oldRaw);
          if (!Number.isNaN(oldNum) && !(current[0] > oldNum)) return '新杂质必须大于前面的杂质';
        }
        return true;
      },
    },
    {
      name: 'impurityFour',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.impurityFour`).d('杂质4'),
      validator: (value, _name, record) => {
        if (value === undefined || value === null || value === '' || value === '-') return true;
        const parseRange = (val: string): [number, number] | null => {
          if (val.includes('-')) {
            const [minStr, maxStr] = val.split('-');
            const min = Number(minStr);
            const max = Number(maxStr);
            if (Number.isNaN(min) || Number.isNaN(max)) return null;
            if (!(min <= max)) return null;
            return [min, max];
          }
          const n = Number(val);
          if (Number.isNaN(n)) return null;
          return [n, n];
        };
        const current = parseRange(String(value));
        if (!current) return '请输入数字或范围，如 12 或 12-34';

        // 与前一个比较：当前最小值必须大于前一个最大值
        const prevRaw = (record as any)?.get?.('impurityThree');
        if (prevRaw && prevRaw !== '-') {
          const prev = parseRange(String(prevRaw));
          if (prev && !(current[0] > prev[1])) return '数据最小值必须大于前一个的最大值';
        }

        // 旧值比较
        const oldRaw = (record as any)?.get?.('oldImpurityFour');
        if (oldRaw && oldRaw !== '-') {
          const oldNum = Number(oldRaw);
          if (!Number.isNaN(oldNum) && !(current[0] > oldNum)) return '新杂质必须大于前面的杂质';
        }
        return true;
      },
    },
    {
      name: 'joinOne',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.joinOne`).d('接头1'),
    },
    {
      name: 'joinTwo',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.joinTwo`).d('接头2'),
    },
  ],
});

export { tableDS, FormDS, oldEoNumberFormDS, newEoNumberFormDS };
