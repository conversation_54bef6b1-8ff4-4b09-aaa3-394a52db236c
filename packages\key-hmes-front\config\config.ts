import { extendParentConfig } from '@hzerojs/plugin-micro';

export default extendParentConfig({
  webpack5: {},
  routes: [
    {
      path: '/hmes/production-maintenance',
      authorized: true,
      component: '@/routes/ProductionMaintenance/List',
    },
    {
      path: '/hmes/demand-detail',
      authorized: true,
      component: '@/routes/DemandDetail/List',
    },
    {
      path: '/hmes/glue-film-splicing-plan',
      authorized: true,
      routes: [
        {
          path: '/hmes/glue-film-splicing-plan/list',
          authorized: true,
          component: '@/routes/GlueFilmSplicingPlan',
        },
        {
          path: '/hmes/glue-film-splicing-plan/roll-up',
          authorized: true,
          component: '@/routes/GlueFilmSplicingPlan/components/RollUpList',
        },
      ],
    },
    // 班组工作台
    {
      path: '/hmes/work-bench',
      priority: 10,
      component: '@/routes/calendar/Workbench',
    },
    // 不良记录管理平台
    {
      path: 'hmes/bad-record/platform',
      priority: 10,
      routes: [
        {
          path: '/hmes/bad-record/platform/list',
          priority: 10,
          component: '@/routes/badRecord/Platform/List',
        },
        {
          path: '/hmes/bad-record/platform/detail/:id',
          priority: 10,
          component: '@/routes/badRecord/Platform/Detail',
        },
        {
          path: '/hmes/bad-record/platform/num/:num',
          priority: 10,
          component: '@/routes/badRecord/Platform/Detail',
        },
      ],
    },
    // 物料维护
    {
      path: '/hmes/product/material-manager',
      priority: 10,
      routes: [
        {
          path: '/hmes/product/material-manager/list',
          priority: 10,
          component: '@/routes/product/Material/MaterialList',
        },
        {
          path: '/hmes/product/material-manager/dist/:id',
          priority: 10,
          component: '@/routes/product/Material/MaterialDetail',
        },
        {
          path: '/hmes/product/material-manager/site-assignment/:id',
          priority: 10,
          component: '@/routes/product/Material/MaterialSiteAssignment',
        },
      ],
    },
    // 每日计划
    {
      path: '/hmes/daily-schedule',
      component: '@/routes/DailySchedule',
    },
    {
      path: '/hmes/material-prevent-error',
      routes: [
        {
          path: '/hmes/material-prevent-error/list',
          component: '../routes/MaterialPreventError/MaterialPreventErrorList',
        },
        {
          path: '/hmes/material-prevent-error/detail/:id',
          component: '../routes/MaterialPreventError/MaterialPreventErrorDetail',
        },
        {
          path: '/hmes/material-prevent-error/import/:code',
          component: '../routes/MaterialPreventError/MaterialPreventErrorList/CommentImport',
        },
      ],
    },
    {
      path: '/hmes/assemblyOrderDispatch',
      title: '装配工单派工',
      routes: [
        {
          path: '/hmes/assemblyOrderDispatch',
          component: '@/pages/AssemblyOrderDispatch',
        },
      ],
    },
    {
      path: '/hmes/circulationCardActivate',
      title: '流转卡开卡',
      routes: [
        {
          path: '/hmes/circulationCardActivate/list',
          component: '@/pages/CirculationCardActivate',
        },
        {
          path: '/hmes/circulationCardActivate/detail/:id',
          component: '@/pages/CirculationCardActivate/Detail',
        },
      ],
    },
    // 执行作业管理
    {
      path: '/hmes/workshop/execute-operation-management',
      routes: [
        {
          path: '/hmes/workshop/execute-operation-management/list',
          component: '@/pages/Execute/ExecuteList',
        },
        {
          path: '/hmes/workshop/execute-operation-management/detail/:id',
          component: '@/pages/Execute/ExecuteDetail',
        },
      ],
    },
    // 物料生产属性维护
    {
      path: '/hmes/product/produce',
      priority: 10,
      routes: [
        {
          path: '/hmes/product/produce/list',
          component: '@/routes/product/Produce/ProduceList',
          priority: 10,
        },
        {
          path: '/hmes/product/produce/dist/:type/:id',
          component: '@/routes/product/Produce/ProduceDist',
          priority: 10,
        },
      ],
    },
    {
      path: '/hmes/downgrade-scroll-plan/list',
      component: '@/routes/DowngradeScrollPlan',
      authorized: true,
    },
    // 在制品报表
    {
      path: '/hmes/work-in-progress-report',
      title: '在制品报表',
      component: '../routes/WorkInProgressReport',
    },
    // 工序作业平台
    {
      path: '/hmes/operation-platform',
      authorized: true,
      title: '工序作业平台',
      component: '../routes/OperationPlatform',
    },
    // 树脂粉工作台
    {
      path: '/hmes/tree-grease-work-table',
      title: '树脂粉工作台',
      component: '../routes/TreeGreaseWorkTable',
    },
    // 胶膜工作台
    {
      path: '/hmes/adhesive-film-workbench',
      title: '胶膜工作台',
      component: '../routes/AdhesiveFilmWorkbench',
    },
    // 翻卷工作台
    {
      path: '/hmes/roll-over-work-order',
      title: '翻卷工作台',
      component: '../routes/RollOverWorkOrder',
    },
    // 打包工作台
    // {
    //   path: '/hmes/packing-table',
    //   title: '打包工作台',
    //   component: '../routes/PackingTable',
    // },
    {
      path: '/hmes/packing-table',
      title: '打包工作台',
      component: '../routes/PackingTableWorkbench',
    },
    // 机加工序作业平台
    {
      authorized: true,
      path: '/pub/hmes/machine-operation-platform',
      routes: [
        {
          authorized: true,
          path: '/pub/hmes/machine-operation-platform/enter',
          component: '../routes/MachineOperationPlatform/EnterModal',
        },
        {
          authorized: true,
          path: '/pub/hmes/machine-operation-platform/list',
          component: '../routes/MachineOperationPlatform',
          models: '../routes/MachineOperationPlatform/model.ts',
        },
      ],
    },
    // 返修作业平台
    {
      path: '/hmes/repair-operation-platform',
      authorized: true,
      routes: [
        {
          path: '/hmes/repair-operation-platform/enter',
          component: '@/routes/RepairOperationPlatform/EnterModal',
        },
        {
          path: '/hmes/repair-operation-platform/list',
          component: '@/routes/RepairOperationPlatform',
          models: '@/routes/RepairOperationPlatform/model.ts',
        },
      ],
    },
    // 异常处理平台
    {
      path: '/hmes/exception-handling-platform/:workcellId',
      models: '@/models/exceptionHandlingPlatform',
      component: '@/routes/ExceptionHandlingPlatform',
      priority: 1000,
    },
    // 异常信息维护
    {
      path: '/hmes/abnormal-info',
      priority: 1000,
      routes: [
        {
          path: '/hmes/abnormal-info/list',
          component: '@/routes/AbnormalInfo',
          models: '@/models/abnormalInfo',
          priority: 1000,
        },
        {
          path: '/hmes/abnormal-info/:code',
          component: '../routes/MaterialPreventError/MaterialPreventErrorList/CommentImport',
          priority: 1000,
        },
      ],
    },
    // 异常收集组维护
    {
      path: '/hmes/abnormal-collection',
      priority: 1000,
      routes: [
        {
          path: '/hmes/abnormal-collection/list',
          component: '@/routes/AbnormalCollection/List',
          models: '@/models/abnormalCollection',
          priority: 1000,
        },
        {
          path: '/hmes/abnormal-collection/detail/:id',
          component: '@/routes/AbnormalCollection/Detail',
          models: '@/models/abnormalCollection',
          priority: 1000,
        },
        {
          path: '/hmes/abnormal-collection/:code',
          component: '../routes/MaterialPreventError/MaterialPreventErrorList/CommentImport',
          priority: 1000,
        },
      ],
    },
    // 异常原因维护
    {
      path: '/hmes/abnormal-reason-maintain',
      routes: [
        {
          path: '/hmes/abnormal-reason-maintain/list',
          component: '@/routes/AbnormalReasonMaintain',
        },
        {
          path: '/hmes/abnormal-reason-maintain/:code',
          component: '../routes/MaterialPreventError/MaterialPreventErrorList/CommentImport',
        },
      ],
    },
    // 异常信息查看报表
    {
      path: '/hmes/abnormal-report',
      models: '@/models/abnormalReport',
      component: '@/routes/AbnormalReport',
    },
    // 安灯类型责任人维护
    {
      path: '/hmes/andeng-responsible-maintain',
      routes: [
        {
          path: '/hmes/andeng-responsible-maintain/list',
          component: '@/routes/AndengResponsible',
          models: ['@/models/andengResponsible'],
        },
        {
          path: '/hmes/andeng-responsible-maintain/:code',
          component: '../routes/MaterialPreventError/MaterialPreventErrorList/CommentImport',
        },
      ],
    },
    // 机加生产派工
    {
      authorized: true,
      path: '/hmes/machine-production-dispatch',
      component: '@/routes/MachineProductionDispatch',
    },
    // 自制件返修指令创建
    {
      // authorized: true,
      path: '/hmes/repair-instruction-created',
      component: '@/routes/RepairInstructionCreated',
    },
    // 工序报工实绩报表
    {
      authorized: true,
      path: '/hmes/process-report-performance-report',
      component: '@/routes/ProcessReportPerformanceReport',
    },
    // 工艺维护
    {
      path: '/hmes/process/technology',
      priority: 10,
      routes: [
        {
          path: '/hmes/process/technology/list',
          component: '@/routes/Technology/TechnologyList',
          priority: 10,
        },
        {
          path: '/hmes/process/technology/dist/:id',
          component: '@/routes/Technology/TechnologyDist',
          priority: 10,
        },
      ],
    },
    // 作业指导书管理
    {
      path: '/hmes/working-instruction-maintenance',
      routes: [
        {
          path: '/hmes/working-instruction-maintenance/list',
          component: '../routes/WorkingInstructionMaintenance/WorkingInstructionMaintenanceList',
        },
        {
          path: '/hmes/working-instruction-maintenance/detail/:id',
          component: '../routes/WorkingInstructionMaintenance/WorkingInstructionMaintenanceDetail',
        },
      ],
    },
    // 装配点维护
    {
      path: '/hmes/equipment-point-maintenance',
      routes: [
        {
          path: '/hmes/equipment-point-maintenance/list',
          priority: 10,
          component: '../routes/EquipmentPointMaintenanceNew/EquipmentPointMaintenanceList',
        },
        {
          path: '/hmes/equipment-point-maintenance/detail/:id',
          priority: 10,
          component: '../routes/EquipmentPointMaintenanceNew/EquipmentPointMaintenanceDetail',
        },
      ],
    },
    // 装配组维护
    {
      path: '/hmes/equipment-group-maintenance',
      routes: [
        {
          path: '/hmes/equipment-group-maintenance/list',
          component: '@/routes/EquipmentGroupMaintenance',
        },
        {
          path: '/hmes/equipment-group-maintenance/:id',
          component: '@/routes/EquipmentGroupMaintenance/Create',
        },
      ],
    },
    // 加工策略配置
    {
      path: '/hmes/processing-strategy-configuration',
      component: '@/routes/ProcessingStrategyConfig',
    },
    // 胶膜制造装配清单
    {
      path: '/hmes/product/glue-film-manufacture-bom',
      priority: 10,
      routes: [
        {
          path: '/hmes/product/glue-film-manufacture-bom/list',
          priority: 10,
          component: '@/routes/product/GlueFilmManufactureBom/index',
        },
      ],
    },
    // 制造装配清单
    {
      path: '/hmes/product/manufacture-list',
      priority: 10,
      routes: [
        {
          path: '/hmes/product/manufacture-list/list',
          priority: 10,
          component: '@/routes/product/C7nManufactureBom/index',
        },
        {
          path: '/hmes/product/manufacture-list/dist/:id',
          priority: 10,
          component: '@/routes/product/C7nManufactureBom/detail',
        },
      ],
    },
    // 物料装配清单
    {
      path: '/hmes/product/assembly-list',
      priority: 10,
      routes: [
        {
          path: '/hmes/product/assembly-list/list',
          priority: 10,
          component: '@/routes/product/C7nMaterialBom/index',
        },
        {
          path: '/hmes/product/assembly-list/dist/:id',
          priority: 10,
          component: '@/routes/product/C7nMaterialBom/detail',
        },
      ],
    },
    // 检验属性维护功能
    {
      path: '/hmes/product/inspection-attributes',
      routes: [
        {
          path: '/hmes/product/inspection-attributes/list',
          component: '@/routes/InspectionAttributes/InspectionAttributesList',
        },
      ],
    },
    // 生产指令管理
    {
      path: '/hmes/workshop/production-order-mgt',
      priority: 10,
      routes: [
        {
          path: '/hmes/workshop/production-order-mgt/list',
          priority: 10,
          component: '@/routes/workshop/ProductionOrderMgt/ProductionOrderMgtList',
        },
        {
          path: '/hmes/workshop/production-order-mgt/detail/:id',
          priority: 10,
          component: '@/routes/workshop/ProductionOrderMgt/ProductionOrderMgtDetail',
        },
      ],
    },
    {
      path: '/hmes/other-instruction-management/list',
      authorized: true,
      component: '@/routes/OtherInstructionManagement',
    },

    // 处置方法维护
    {
      path: '/hmes/bad/disposition-method',
      priority: 999,
      component: '@/routes/DispositionMethod/DispositionMethodList',
    },
    // 站点维护
    {
      path: '/hmes/organization-modeling/site',
      priority: 10,
      routes: [
        {
          path: '/hmes/organization-modeling/site/list',
          component: '@/routes/org/Site/SiteList',
          priority: 10,
        },
        {
          path: '/hmes/organization-modeling/site/detail/:siteId',
          component: '@/routes/org/Site/SiteDetail',
          priority: 10,
        },
      ],
    },
    // 库位维护
    {
      path: '/hmes/organization-modeling/locator',
      priority: 10,
      routes: [
        {
          path: '/hmes/organization-modeling/locator/list',
          component: '@/routes/org/Locator/LocatorList',
          priority: 10,
        },
        {
          path: '/hmes/organization-modeling/locator/detail/:locatorId',
          component: '@/routes/org/Locator/LocatorDetail',
          priority: 10,
        },
      ],
    },
    // 组织关系维护
    {
      path: '/hmes/organization-modeling/relation-maintenance',
      component: '@/routes/org/RelationMaintain',
      model: '@/models/org/relationMaintain',
      priority: 10,
    },
    // 打印模板配置
    {
      path: '/hmes/print-template-configuration',
      authorized: true,
      routes: [
        {
          path: '/hmes/print-template-configuration/list',
          component: '@/routes/PrintTemplateConfiguration/index.js',
          authorized: true,
        },
        {
          path: '/hmes/print-template-configuration/detail/:templateId',
          component: '@/routes/PrintTemplateConfiguration/detail.js',
          authorized: true,
        },
      ],
    },

    // 用户权限维护
    {
      path: '/hmes/mes/user-rights',
      priority: 10,
      component: '@/routes/hmes/UserRights',
    },
    // 质量追溯报表
    {
      path: '/hmes/quality-traceability-report',
      component: '@/routes/QualityTraceabilityReport',
    },
    // 员工上下岗
    {
      path: '/group/employee-clock',
      priority: 999,
      routes: [
        {
          path: '/group/employee-clock/list',
          component: '@/routes/group/EmployeeClock/EmployeeClockList',
          priority: 999,
        },
      ],
    },
    // 消息处理查询
    {
      path: '/message/message-processing',
      title: '消息处理查询',
      component: '@/routes/message/MessageProcessing',
    },
    // Bartender模版维护
    {
      path: '/hmes/bartender-template-maintain',
      title: 'Bartender模版维护',
      component: '@/routes/BartenderTemplateMaintain/List',
    },
    // 指令模版维护
    {
      path: '/hmes/print-instruction-maintain',
      title: '指令模版维护',
      component: '@/routes/PrintInstructionMaintain/List',
    },

    // 产品加工履历查询报表
    {
      path: '/hmes/product-processing-history-query-report',
      component: '@/routes/ProductProcessingHistoryQueryReport',
    },

    // 装配记录查询
    {
      path: '/hmes/assembly-record-query',
      component: '@/routes/AssemblyRecordQuery',
    },

    // 产品加工参数查询报表
    {
      path: '/hmes/product-processing-parameter-query',
      component: '@/routes/ProductProcessingParameterQuery',
    },

    // 报工事物报表平台
    {
      path: '/hmes/work-transaction-report/platform',
      component: '@/routes/transactionReport/WorkTransactionReportPlatform',
    },

    // 报工事物明细报表
    {
      path: '/hmes/work-transaction-report/transaction-detail-report',
      component: '@/routes/transactionReport/WorkTransactionDetailReport',
    },

    // 报工事件明细报表
    {
      path: '/hmes/work-transaction-report/mobile-event-detail-report',
      component: '@/routes/transactionReport/WorkMobileEventDetailReport',
    },
    
    // 树脂粉打包合批单
    {
      path: '/hmes/resin-packing-list',
      routes: [
        {
          path: '/hmes/resin-packing-list/list',
          component: '@/routes/ResinPackingList',
        },
      ],
    },
    // 对象数据收集组管关系查询
    {
      path: '/hmes/acquisition/tag-group-object',
      routes: [
        {
          path: '/hmes/acquisition/tag-group-object/list',
          component: '@/routes/TagGroupObjectQuery/TagGroupObjectList',
        },
      ],
    },
    // 事务明细报表
    {
      path: '/hmes/transaction-report/transaction-detail-report',
      component: '@/routes/transactionReport/TransactionDetailReport',
      priority: 10,
    },
    // 事务报表平台
    {
      path: '/hmes/transaction-report/platform',
      component: '@/routes/transactionReport/TransactionReportPlatform',
      priority: 10,
    },
    // 移动事件明细报表
    {
      path: '/hmes/transaction-report/mobile-event-detail-report',
      component: '@/routes/transactionReport/MobileEventDetailReport',
      priority: 10,
    },
    // 片粉料清单报表
    {
      path: '/hmes/powder-material-list-report',
      component: '@/routes/PowderMaterialListReport',
      priority: 10,
      authorized: true,
      title: '片粉料清单报表',
    },

    // 物料存储属性维护
    {
      path: '/hmes/product/pfep-inventory',
      priority: 10,
      routes: [
        {
          path: '/hmes/product/pfep-inventory/list',
          component: '@/routes/product/PfepInventory/PfepInventoryList',
          priority: 10,
        },
        {
          path: '/hmes/product/pfep-inventory/dist/:type/:id',
          component: '@/routes/product/PfepInventory/PfepInventoryDetail',
          priority: 10,
        },
      ],
    },
    // 事件事务转换关系维护
    {
      path: '/hmes/event/transaction/type/rel-maintenance',
      component: '@/routes/event/TransactionTypeRelMaintenance',
      priority: 10,
    },
    // 锯管芯任务列表
    {
      path: '/hmes/saw-core-task-list',
      component: '@/routes/SawCoreTaskList',
    },
    // 工作单元维护
    {
      path: '/hmes/organization-modeling/work-cell',
      priority: 10,
      routes: [
        {
          path: '/hmes/organization-modeling/work-cell/list',
          component: '@/routes/org/WorkCell/WorkCellList',
          priority: 10,
        },
        {
          path: '/hmes/organization-modeling/work-cell/detail/:workcellId',
          component: '@/routes/org/WorkCell/WorkCellDetail',
          priority: 10,
        },
      ],
    },
    // 投料计划单
    {
      path: '/hmes/plan/material-feeding-plan',
      routes: [
        {
          path: '/hmes/plan/material-feeding-plan/list',
          component: '@/routes/MaterialFeedingPlan/PlanList',
          title: '投料计划单',
          authorized: true,
        },
        {
          path: '/hmes/plan/material-feeding-plan/list/:workOrderId/:routerId',
          component: '@/routes/MaterialFeedingPlan/PlanList',
          title: '投料计划单',
          authorized: true,
        },
      ],
    },
    // 树脂粉班组交接信息报表
    {
      path: '/hmes/treeGreaseConnectInfoReport',
      component: '@/routes/TreeGreaseConnectInfoReport',
    },
    // 树脂粉车间操作记录
    {
      path: '/hmes/resin-operation-records',
      component: '@/routes/ResinOperationRecords',
    },
    // 企业维护
    {
      path: '/hmes/organization-modeling/enterprise',
      component: '@/routes/org/Enterprise',
      priority: 1000,
    },
    // 镀铝膜产品规格表
    {
      path: '/hmes/aluminizer-specification',
      component: '@/routes/AluminizerSpecification',
      title: '镀铝膜产品规格表',
      authorized: true,
    },
    {
      path: '/hmes/organization-modeling/customer',
      priority: 1000,
      routes: [
        {
          path: '/hmes/organization-modeling/customer/list',
          component: '@/routes/org/Customer',
          priority: 1000,
        },
        {
          path: '/hmes/organization-modeling/customer/detail/:id',
          component: '@/routes/org/Customer/Detail',
          priority: 1000,
        },
      ],
    },
  ],
  hash: true,
  hzeroMicro: {
    // microConfig: {
    //   registerRegex: '\\/.*',
    // },
  },
  // 如果存在发布 lib 包需求,可以解开该配置，对应 babelrc 中的内容
  // 注意若父模块与子模块都配置了module-resolver插件,请保证数组的第三个参数不能为同一个字符串或者都为空
  extraBabelPlugins: [
    [
      'module-resolver',
      {
        root: ['./'],
        alias: {
          '@': './src',
        },
      },
    ],
  ],
});
