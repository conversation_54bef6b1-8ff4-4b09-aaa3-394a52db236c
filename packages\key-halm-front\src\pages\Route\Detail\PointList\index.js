import React, { Component } from 'react';
import { Table, Button, Modal } from 'choerodon-ui/pro';
import { enableRender } from 'utils/renderer';
import { Bind } from 'lodash-decorators';
import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_MTC } from 'alm/utils/config';
import request from 'utils/request';
import notification from 'utils/notification';
import PointModal from 'alm/components/PointLov/PointModal';

import getLangs from '../../Langs';

const { TableRow } = Table;
const organizationId = getCurrentOrganizationId();

export default class PointList extends Component {
  constructor(props) {
    super(props);
    this.modalRef = React.createRef();
  }

  @Bind
  handleCreate() {
    const { maintSiteId } = this.props;
    Modal.open({
      key: 'point_lov',
      destroyOnClose: true,
      closable: true,
      style: {
        width: 700,
      },
      title: getLangs('TITLE_POINT'),
      children: (
        <PointModal ref={this.modalRef} multiSelect queryParams={{ maintSiteIds: maintSiteId }} />
      ),
      onOk: this.handleModalOk,
    });
  }
  
  @Bind
  handleSort() {
    const sortData = this.props.pointListDs.toData();
    sortData.sort((a, b) => {
      if (a.pointCode < b.pointCode) return -1;
      if (a.pointCode > b.pointCode) return 1;
      return 0;
    });
    sortData.forEach((item,index)=>{
      item.sequenceNumber = index+1
    })
    this.props.pointListDs.loadData(sortData)
  }

  @Bind
  handleModalOk() {
    if (this.modalRef.current?.tableDs.selected.length > 0) {
      const routeId = this.props.detailDs?.current.get('routeId');
      const body = this.modalRef.current?.tableDs.selected.map(i => {
        const { pointId, _token, objectVersionNumber } = i.toData();
        return { pointId, _token, objectVersionNumber, tenantId: organizationId, routeId };
      });
      request(`${HALM_MTC}/v1/${organizationId}/routes/save-point-route-rel`, {
        method: 'POST',
        body,
      }).then(res => {
        if (res && !res.failed) {
          this.props.pointListDs.query();
        } else {
          notification.error({
            message: res?.message,
          });
        }
      });
    } else {
      notification.error({
        message: getLangs('NO_SELECT'),
      });
      return false;
    }
  }

  // 移除
  @Bind
  handleRemove(ds) {
    const { record, dataSet } = ds;
    const routeId = this.props.detailDs?.current.get('routeId');
    const currPointId = record.get('pointId');
    const otherData = dataSet.filter(i => i.get('pointId') !== currPointId);
    const pointRouteRelDTOList = otherData.map((i, _index) => {
      const { objectVersionNumber, _token, pointId, id } = i.toData();
      return {
        tenantId: organizationId,
        objectVersionNumber,
        _token,
        pointId,
        sequenceNumber: _index + 1,
        routeId,
        id,
      };
    });

    request(`${HALM_MTC}/v1/${organizationId}/routes/remove-point-route-rel`, {
      method: 'DELETE',
      body: {
        pointRouteRelDTO: { ...record.toData(), routeId },
        pointRouteRelDTOList,
      },
    }).then(res => {
      if (res && res.failed) {
        notification.error({
          message: res.message,
        });
      } else if (res && !res.failed) {
        if (otherData.length === 0) {
          // 如果行数据全删了 这个时候会将头数据的 是否启用 重置为 否，需要进行提示并刷新头数据的是否启用字段
          const tempData = this.props.detailDs.current.toData();
          Modal.warning({
            title: getLangs('NOTICE'),
            children: getLangs('DIS_ROUTE'),
            closable: true,
            footer: [],
            onCancel: () => {
              this.props.detailDs.query().then(resData => {
                this.props.detailDs.current.set({
                  ...tempData,
                  objectVersionNumber: resData.objectVersionNumber,
                  enabledFlag: 0,
                });
              });
              this.props.pointListDs.query();
            },
          });
        } else {
          this.props.pointListDs.query();
        }
      }
    });
  }

  renderDragRow(props) {
    // eslint-disable-next-line no-param-reassign
    delete props.dragColumnAlign;
    return <TableRow {...props} />;
  }

  @Bind()
  onDragEnd(dataSet) {
    dataSet.forEach((item, index) => {
      item.set('sequenceNumber', index + 1);
    });
  }

  get columns() {
    const { isEdit } = this.props;
    const cols = [
      {
        name: 'sequenceNumber',
        width: 60,
      },
      {
        name: 'pointName',
      },
      {
        name: 'pointCode',
        width: 150,
      },
      {
        name: 'pointType',
      },
      {
        name: 'pointObjectName',
        tooltip: 'overflow',
      },
      {
        name: 'enabledFlag',
        width: 100,
        align: 'center',
        renderer: ({ value }) => enableRender(value),
      },
    ];
    const opCol = [
      {
        header: getLangs('OPTION'),
        width: 60,
        renderer: ds => {
          return <a onClick={() => this.handleRemove(ds)}>{getLangs('REMOVE')}</a>;
        },
      },
    ];
    return isEdit ? cols.concat(opCol) : cols;
  }

  render() {
    const { isEdit, pointListDs } = this.props;
    return (
      <React.Fragment>
        {isEdit && (
          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button icon="sort" onClick={this.handleSort}>
              {getLangs('SORT')}
            </Button>
            <Button icon="add" onClick={this.handleCreate}>
              {getLangs('CREATE')}
            </Button>
          </div>
        )}
        <Table
          key="routePoint"
          customizedCode="AORI.ROUTE.POINT"
          dataSet={pointListDs}
          columns={this.columns}
          dragRow={isEdit}
          dragColumnAlign="left"
          rowDragRender={{ renderClone: this.renderDragRow }}
          onDragEnd={this.onDragEnd}
        />
      </React.Fragment>
    );
  }
}
