/*
 * @workOrder 胶膜工作台-翻卷工单卡片
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-10-29 13:52:47
 * @copyright Copyright (c) 2024 , Hand
 */
/* eslint-disable no-console */
import React, { useMemo, useEffect, useState, useRef } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet, Modal, Form, TextField, Button } from 'choerodon-ui/pro';
import moment from 'moment';
import { ColumnProps, TableQueryBarType } from 'choerodon-ui/pro/lib/table/interface';
import notification from 'utils/notification';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { LabelLayout } from 'choerodon-ui/pro/lib/form/enum';
import { useDataSetEvent } from 'utils/hooks';
import intl from 'utils/intl';
// import { TemplatePrintButton } from '../../../../components/tarzan-ui';
import TemplatePrintButton from '../../TemplatePrintButton';
import { CardLayout, useRequest, ONotification } from '../commonComponents';
import { useRollOverWorkOrder } from '../../contextsStore';
import { tableDS, FormDS, oldEoNumberFormDS, newEoNumberFormDS } from './stores';
import {
  DeleteTableLine,
  CreateNewEONumber,
  ExecuteTableLine,
  IotAutoQuery,
  DegradeExecute,
  ImpurityCreat,
  fetchAndGeneratePrintTemplate,
  TransactionSummary,
} from './services';
import styles from './index.module.less';

const modelPrompt = 'tarzan.rollOverWorkOrder';

const RollOverWorkOrder = observer(props => {
  const { enterInfo } = useRollOverWorkOrder();

  const [repPrintStatus, setRepPrintStatus] = useState(false);
  const [printStatus, setPrintStatus] = useState(false);
  const [repPrintParams, setRepPrintParams] = useState<any>({});
  const [printParams, setPrintParams] = useState<any>({});
  const [highlightedRow, setHighlightedRow] = useState<number | null>(null); // 高亮行索引
  const print = useRef(null);
  const repPrint = useRef(null);

  const printTemplate = useRef('');
  const [highlightedField, setHighlightedField] = useState<'old' | 'new' | null>(null);
  const { run: deleteTableLine, loading: deleteTableLineLoading } = useRequest(DeleteTableLine(), {
    manual: true,
    needPromise: true,
  });
  const { run: createNewEONumber, loading: createNewEONumberLoading } = useRequest(
    CreateNewEONumber(),
    {
      manual: true,
      needPromise: true,
    },
  );
  const { run: impurityCreat, loading: ImpurityCreatLoading } = useRequest(ImpurityCreat(), {
    manual: true,
    needPromise: true,
  });
  const { run: executeTableLine, loading: executeTableLineLoading } = useRequest(
    ExecuteTableLine(),
    {
      manual: true,
      needPromise: true,
    },
  );
  const { run: iotAutoQuery } = useRequest(IotAutoQuery(), {
    manual: true,
    needPromise: true,
  });

  const { run: degradeExecute, loading: degradeExecuteLoading } = useRequest(DegradeExecute(), {
    manual: true,
    needPromise: true,
  });

  // 事务汇总
  const { run: transactionSummary, loading: transactionSummaryLoading } = useRequest(
    TransactionSummary(),
    {
      manual: true,
      needPromise: true,
    },
  );

  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const formDs = useMemo(() => new DataSet(FormDS()), []);
  const oldEoNumberFormDs = useMemo(() => new DataSet(oldEoNumberFormDS()), []);
  const newEoNumberFormDs = useMemo(() => new DataSet(newEoNumberFormDS()), []);

  useEffect(() => {
    if (enterInfo?.prodLineId) {
      tableDs.setQueryParameter('productLineId', enterInfo?.prodLineId);
      tableDs.setQueryParameter('startTime', moment(new Date()).format('YYYY-MM-DD 00:00:00'));
      tableDs.setQueryParameter('endTime', moment(new Date()).format('YYYY-MM-DD 23:59:59'));
      tableDs.query();
    }
  }, [enterInfo]);

  const clearSearchState = () => {
    setHighlightedRow(null);
    setHighlightedField(null);
    tableDs.unSelectAll();
  };

  const handleSearch = () => {
    if (!tableDs.queryDataSet) return;

    const keyword = String(tableDs.queryDataSet?.current?.get('identification') || '').trim();
    if (!keyword) return clearSearchState();

    const index = tableDs.data.findIndex(record => {
      const oldVal = record.get('oldEoNumber') || '';
      const newVal = record.get('newEoNumber') || '';
      return (
        (typeof oldVal === 'string' && oldVal.includes(keyword)) ||
        (typeof newVal === 'string' && newVal.includes(keyword))
      );
    });

    if (index < 0) return clearSearchState();

    const record = tableDs.data[index];
    const newVal = record.get('newEoNumber') || '';
    setHighlightedRow(index);
    setHighlightedField(typeof newVal === 'string' && newVal.includes(keyword) ? 'new' : 'old');
    selectAndLocateRow(record);
  };

  const selectAndLocateRow = record => {
    if (!record) return;
    tableDs.unSelectAll();
    tableDs.select(record);
    tableDs.current = record;

    setTimeout(() => {
      const tableRoot = document.getElementById(styles.tableInnerField);
      if (!tableRoot) return;
      const body = tableRoot.querySelector('.c7n-pro-table-body') as HTMLElement | null;
      if (!body) return;
      const rowEl = body.querySelector(`tr.${styles.rowHighlight}`) as HTMLElement | null;
      if (rowEl) {
        const bodyRect = body.getBoundingClientRect();
        const rowRect = rowEl.getBoundingClientRect();
        const rowTopInBody = rowRect.top - bodyRect.top + body.scrollTop;
        const rawTop = rowTopInBody - (body.clientHeight - rowEl.clientHeight) / 2;
        const maxTop = Math.max(0, body.scrollHeight - body.clientHeight);
        const targetTop = Math.max(0, Math.min(rawTop, maxTop));
        body.scrollTo({ top: targetTop, behavior: 'smooth' });
      }
    }, 0);
  };

  useDataSetEvent(tableDs, 'update', handleSearch);

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  }, []);

  // 打印
  useEffect(() => {
    console.log(printParams, 'printParams');

    if (printParams?.eoIdList) {
      setTimeout(() => {
        // @ts-ignore
        print.current?.print();
      }, 500);
    }
  }, [printParams]);

  useEffect(() => {
    console.log(repPrintParams, 'repPrintParams');

    if (repPrintParams && repPrintParams[0]?.workOrderId && repPrintParams[0]?.newEoId) {
      setTimeout(() => {
        // @ts-ignore
        repPrint.current?.print();
      }, 500);
    }
  }, [repPrintParams]);

  const handleReprintCertificate = async () => {
    // 单条打印
    // const selectList = tableDs.selected.map(item => item.toData());
    // const params = [
    //   {
    //     workOrderId: selectList[0].workOrderId,
    //     newEoId: selectList[0].newEoId,
    //   },
    // ];
    // 多条打印
    const params = tableDs.selected.map((item)=>{
      return {
        workOrderId: item.get('workOrderId'),
        newEoId: item.get('newEoId'),
      }
    })
    const template = await fetchAndGeneratePrintTemplate(
      '/roll-work-order-card/print',
      params,
      'HME.ROLL_WORK_ORDER',
      'POST',
    );

    if (!template) {
      setRepPrintStatus(false);
      return;
    }

    printTemplate.current = template;
    setRepPrintStatus(true);
    setRepPrintParams(params);
  };

  const listener = flag => {
    // 搜索条件监听
    if (tableDs.queryDataSet) {
      const handler = flag
        ? tableDs.queryDataSet.addEventListener
        : tableDs.queryDataSet.removeEventListener;
      handler.call(tableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
      handler.call(tableDs.queryDataSet, 'update', handleSearch);
    }
  };

  const handleQueryDataSetUpdate = ({ record, name }) => {
    tableDs.setQueryParameter(
      'startTime',
      moment(record.get('startTime')).format('YYYY-MM-DD 00:00:00'),
    );
    tableDs.setQueryParameter(
      'endTime',
      moment(record.get('endTime')).format('YYYY-MM-DD 23:59:59'),
    );

    if (name !== 'identification') {
      tableDs.query();
    }
  };

  const handleGain = async dataSet => {
    const res = await iotAutoQuery({
      params: {
        workcellId: tableDs.selected.length ? tableDs.selected[0]?.get('workcellId') : null,
        tagCodeList: ['WEIGHT'],
      },
    });
    if (res?.success) {
      if (res.rows.length) {
        const totalWeight = res.rows.filter(item => item.tagCode === 'WEIGHT')[0].tagValue;
        dataSet.current?.set('actualWeight', totalWeight);
      } else {
        ONotification.error({ message: '未获取到设备采集信息，请检查！' });
        return false;
      }
    } else {
      ONotification.error({ message: res?.message });
      return false;
    }
  };

  // 新卷号生成
  const handleCreateNewEONumClick = () => {
    const data = tableDs?.selected[0];
    let actualWeightFlag = false;
    const {
      newVolumeMeter,
      oldVolumeMeter,
      sourceVolumeFlag,
      oldGrossWeight,
    } = tableDs?.selected[0].toData();
    if (newVolumeMeter === oldVolumeMeter && sourceVolumeFlag === 'Y') {
      data.set('actualWeight', oldGrossWeight);
      actualWeightFlag = true;
    }
    formDs.loadData([data.toData()]);
    Modal.open({
      title: '新卷号生成',
      key: Modal.key(),
      style: {
        width: '40%',
      },
      contentStyle: {
        background: '#38708F',
        color: '#FFF',
      },
      className: styles.doneStepFlagModals,
      children: (
        <Form
          className={styles.modalFormExceptionReporting}
          labelLayout={LabelLayout.horizontal}
          columns={1}
          labelWidth={180}
          dataSet={formDs}
        >
          <TextField name="oldProdModel" />
          <TextField name="oldEoNumber" />
          <TextField name="newEoNumber" />
          <TextField name="planWeight" />
          <TextField
            name="actualWeight"
            restrict="0.1-9"
            suffix={
              !actualWeightFlag && (
                <div
                  id={styles.buttonPrimaryGainBtn}
                  style={{ cursor: 'pointer' }}
                  onClick={() => handleGain(formDs)}
                >
                  {intl.get(`${modelPrompt}.gain`).d('获取')}
                </div>
              )
            }
          />
        </Form>
      ),
      onOk: async () => {
        const validate = await formDs.current?.validate(true);
        if (!validate) {
          notification.warning({
            message: '存在必输字段，请检查！',
          });
          return false;
        }
        const data = formDs?.current?.toData();
        // 判断实际重量是否大于0
        if (data.actualWeight <= 0) {
          notification.warning({
            message: '实际重量必须大于0',
          });
          return false;
        }
        createNewEONumber({
          params: {
            ...data,
            siteId: enterInfo?.siteId,
          },
        }).then(async res => {
          if (res?.success) {
            notification.success({
              message: '操作成功',
              description: undefined,
            });
            await tableDs.query();
          }
        });
      },
    });
  };

  // 删除指令单
  const handleDeleteClick = () => {
    if (!tableDs.selected?.length) {
      return;
    }
    const comletedData = tableDs.selected?.filter(
      item => item?.get('workOrderStatus') === 'COMPLETED',
    );
    if (comletedData?.length) {
      const workOrderNumList = comletedData?.map(item => item?.get('workOrderNum')).join(',');
      return notification.warning({
        message: `降级工单${workOrderNumList}已完成，不允许取消，请检查！`,
      });
    }

    Modal.confirm({
      iconType: '',
      key: Modal.key(),
      contentStyle: {
        background: '#38708F',
      },
      className: styles.doneStepFlagModals,
      children: <span style={{ color: '#FFF' }}>是否将选中数据删除，请确认！</span>,
      onOk: () => {
        deleteTableLine({
          params: { workOrderIds: tableDs.selected.map(item => item?.get('workOrderId')) },
        }).then(async res => {
          if (!res?.failed) {
            notification.success({
              message: '操作成功',
              description: undefined,
            });
            await tableDs.query();
          }
        });
      },
    });
  };

  // 报检
  const handleExcuteClick = () => {
    if (!tableDs.selected?.length) {
      return;
    }
    const comletedData = tableDs.selected?.map(item => item.toData());
    executeTableLine({
      params: {
        ...comletedData[0],
        siteId: enterInfo?.siteId,
      },
    }).then(async res => {
      if (res?.success) {
        notification.success({
          message: '操作成功',
          description: undefined,
        });
        await tableDs.query();
      } else {
        notification.error({
          message: '操作失败',
          description: res?.message,
        });
      }
    });
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        title: '序号',
        width: 80,
        align: ColumnAlign.center,
        renderer: ({ record }) => {
          const index = (record?.index || 0) + 1;
          return index > 9 ? index : `0${index}`;
        },
      },
      {
        name: 'workOrderNum',
        width: 180,
        align: ColumnAlign.center,
      },
      {
        name: 'oldEoNumber',
        width: 180,
        align: ColumnAlign.center,
        renderer: ({ record, value }) => {
          if (!record) return value;
          const recordIndex = tableDs.data.indexOf(record);
          const isHighlighted = highlightedRow === recordIndex && highlightedField === 'old';
          return (
            <span
              style={{
                backgroundColor: isHighlighted ? '#ffeb3b' : 'transparent',
                padding: '2px 4px',
                borderRadius: '2px',
              }}
            >
              {value}
            </span>
          );
        },
      },
      {
        name: 'newEoNumber',
        align: ColumnAlign.center,
        width: 180,
        renderer: ({ record, value }) => {
          if (!record) return value;
          const recordIndex = tableDs.data.indexOf(record);
          const isHighlighted = highlightedRow === recordIndex && highlightedField === 'new';
          return (
            <span
              style={{
                backgroundColor: isHighlighted ? '#ffeb3b' : 'transparent',
                padding: '2px 4px',
                borderRadius: '2px',
              }}
            >
              {value}
            </span>
          );
        },
      },
      {
        name: 'workOrderStatusDesc',
        align: ColumnAlign.center,
      },
      {
        name: 'planStartTime',
        width: 180,
        align: ColumnAlign.center,
        renderer: ({ value }) => {
          return moment(value).format('YYYY-MM-DD');
        },
      },
      {
        name: 'oldProdModel',
        width: 150,
        align: ColumnAlign.center,
      },
      {
        name: 'newProdModel',
        width: 150,
        align: ColumnAlign.center,
      },
      {
        name: 'prodDesc',
        align: ColumnAlign.center,
      },
      {
        name: 'prodTargetValue',
        align: ColumnAlign.center,
      },
      {
        name: 'color',
        align: ColumnAlign.center,
      },
      {
        name: 'oldVolumeMeter',
        align: ColumnAlign.center,
      },
      {
        name: 'newVolumeMeter',
        align: ColumnAlign.center,
      },
      {
        name: 'progressFlag',
        align: ColumnAlign.center,
        renderer: ({ value }) => {
          if (value === 'Y') {
            return <span style={{ color: '#23c089' }}>已同步</span>;
          }
          return '未同步';
        },
      },
    ];
  }, [highlightedRow, highlightedField]);

  const onRow = ({ record }) => {
    const recordIndex = tableDs.data.indexOf(record);
    const isHighlighted = highlightedRow === recordIndex;
    return {
      onClick: () => {
        tableDs.data.forEach(item => {
          item.isSelected = false;
        });
        record.isSelected = true;
      },
      className: isHighlighted ? styles.rowHighlight : undefined,
    };
  };

  const handleImpurityInputClick = () => {
    const workOrderId = tableDs?.selected[0].get('workOrderId');
    const newEoNumber = tableDs?.selected[0].get('newEoNumber');
    oldEoNumberFormDs?.current?.set('impurityOne', tableDs?.selected[0].get('impurityOne'));
    oldEoNumberFormDs?.current?.set('impurityTwo', tableDs?.selected[0].get('impurityTwo'));
    oldEoNumberFormDs?.current?.set('impurityThree', tableDs?.selected[0].get('impurityThree'));
    oldEoNumberFormDs?.current?.set('impurityFour', tableDs?.selected[0].get('impurityFour'));
    oldEoNumberFormDs?.current?.set('joinOne', tableDs?.selected[0].get('joinOne'));
    oldEoNumberFormDs?.current?.set('joinTwo', tableDs?.selected[0].get('joinTwo'));
    newEoNumberFormDs?.current?.set('oldImpurityOne', tableDs?.selected[0].get('impurityOne'));
    newEoNumberFormDs?.current?.set('oldImpurityTwo', tableDs?.selected[0].get('impurityTwo'));
    newEoNumberFormDs?.current?.set('oldImpurityThree', tableDs?.selected[0].get('impurityThree'));
    newEoNumberFormDs?.current?.set('oldImpurityFour', tableDs?.selected[0].get('impurityFour'));
    Modal.open({
      title: '卷号资料同步',
      key: Modal.key(),
      style: {
        width: '70%',
      },
      contentStyle: {
        background: '#38708F',
        color: '#FFF',
      },
      className: styles.doneStepFlagModals,
      children: (
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div>
            <Form
              className={styles.modalFormExceptionReporting}
              labelLayout={LabelLayout.horizontal}
              columns={1}
              labelWidth={180}
              dataSet={oldEoNumberFormDs}
              header="原卷号"
            >
              <TextField name="impurityOne" />
              <TextField name="impurityTwo" />
              <TextField name="impurityThree" />
              <TextField name="impurityFour" />
              <TextField name="joinOne" />
              <TextField name="joinTwo" />
            </Form>
          </div>
          <div style={{ marginLeft: '10px' }}>
            <Form
              className={styles.modalFormExceptionReporting}
              labelLayout={LabelLayout.horizontal}
              columns={1}
              labelWidth={180}
              dataSet={newEoNumberFormDs}
              header="新卷号"
            >
              <TextField name="impurityOne" restrict="0.1-9-" />
              <TextField name="impurityTwo" restrict="0.1-9-" />
              <TextField name="impurityThree" restrict="0.1-9-" />
              <TextField name="impurityFour" restrict="0.1-9-" />
              <TextField name="joinOne" restrict="0.1-9" />
              <TextField name="joinTwo" restrict="0.1-9" />
            </Form>
          </div>
        </div>
      ),
      onOk: async () => {
        // const validate = await newEoNumberFormDs.validate();
        // if (!validate) {
        //   notification.warning({
        //     message: '存在必输字段，请检查！',
        //   });
        //   return false;
        // }
        const data = newEoNumberFormDs?.current?.toData();
        impurityCreat({
          params: {
            impurityOne: data?.impurityOne || null,
            impurityTwo: data?.impurityTwo || null,
            impurityThree: data?.impurityThree || null,
            impurityFour: data?.impurityFour || null,
            joinOne: data?.joinOne || null,
            joinTwo: data?.joinTwo || null,
            workOrderId,
            newEoNumber,
          },
        }).then(async res => {
          if (res?.success) {
            notification.success({
              message: '操作成功',
              description: undefined,
            });
            newEoNumberFormDs.reset();
            await tableDs.query();
          }
        });
      },
    });
  };

  const handlePrintException = async () => {
    // Reset print states
    setPrintStatus(false);
    setPrintParams({});
    printTemplate.current = '';

    const params = {
      eoIdList: (tableDs.selected.map((item)=>{
        return item.get('newEoId')
      })).join(','),
    };

    const template = await fetchAndGeneratePrintTemplate(
      '/hme-production-report/certificate/print/query/ui',
      params,
      'HME.EXCEPRION_PRINT',
    );

    if (!template) {
      return;
    }

    printTemplate.current = template;
    setPrintStatus(true);
    setPrintParams(params);
  };

  // 事务汇总
  const handleTransactionSummary = () => {
    const params = {
      identification: 'FJ_MATERIAL_LOT_CONSUME',
    };

    transactionSummary({ params }).then(async res => {
      if (res?.success) {
        notification.success({
          message: '操作成功',
          description: undefined,
        });
        await tableDs.query();
      }
    });
  };

  return (
    <CardLayout.Layout
      spinning={
        deleteTableLineLoading ||
        createNewEONumberLoading ||
        executeTableLineLoading ||
        degradeExecuteLoading ||
        ImpurityCreatLoading ||
        transactionSummaryLoading
      }
      className={styles.returnScrapWork}
    >
      <CardLayout.Header
        className="SelfMutualInspectionHead"
        title={intl.get(`${modelPrompt}.title`).d('翻卷')}
        help={props?.cardUsage?.remark}
        // addonAfter={
        //   <div className={styles.topRight}>
        //     {enterInfo?.workStationName} {enterInfo?.productionLineCode} {enterInfo?.shiftTeamName} {enterInfo.userName}
        //   </div>
        // }
      />
      <CardLayout.Content className="SelfMutualInspectionForm">
        <Table
          id={styles.tableInnerField}
          dataSet={tableDs}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            queryFieldsLimit: 3,
            autoQuery: false,
            onFieldEnterDown: () => handleSearch(),
          }}
          columns={columns}
          customizedCode="RollOverWorkOrder"
          style={{ height: 'calc(100% - 36px)' }}
          renderEmpty={() => <></>}
          rowHeight={30}
          onRow={onRow}
        />
      </CardLayout.Content>
      <div className={styles.cardFooter}>
        <div className={styles.printButton}>
          <Button
            color={ButtonColor.primary}
            className={styles.buttonOk}
            disabled={
              tableDs.selected?.length !== 1 ||
              (tableDs.selected?.length === 1 && !tableDs?.selected[0]?.get('newEoNumber'))
            }
            onClick={handleImpurityInputClick}
          >
            卷号资料同步
          </Button>
          <Button
            color={ButtonColor.primary}
            className={styles.buttonOk}
            disabled={
              tableDs.selected?.length !== 1 ||
              (tableDs.selected?.length === 1 && tableDs?.selected[0]?.get('newEoNumber'))
            }
            onClick={handleCreateNewEONumClick}
          >
            新卷号生成
          </Button>
          <Button
            color={ButtonColor.primary}
            className={styles.buttonOk}
            disabled={!tableDs.selected?.length}
            onClick={handleDeleteClick}
          >
            删除指令单
          </Button>
          <Button
            color={ButtonColor.primary}
            className={styles.buttonOk}
            disabled={!tableDs.selected?.length || tableDs.selected?.length !== 1}
            onClick={handleExcuteClick}
          >
            报检
          </Button>
          <Button
            style={{
              backgroundColor: tableDs.selected.length === 0 ? '#32617f' : '#00d4cd',
              marginRight: '10px',
            }}
            disabled={
              tableDs.selected?.length === 0 || 
              tableDs.selected.some((item)=>{
                return !item.get('newEoNumber')
              })
            }
            onClick={handlePrintException}
          >
            打印异常
          </Button>
          <div style={{ display: 'none' }}>
            {printStatus && (
              <TemplatePrintButton
                printButtonCode={printTemplate.current}
                icon=""
                ref={print}
                disabled={
                  tableDs.selected?.length === 0 || 
                  tableDs.selected.some((item)=>{
                    return !item.get('newEoNumber')
                  })
                }
                name={intl.get(`${modelPrompt}.exception.print`).d('打印异常')}
                printParams={printParams}
                frUrl="/hme-production-report/certificate/print/query/ui"
              />
            )}
          </div>
          <Button
            className={styles.buttonsSuccess}
            // 单条打印限制
            // disabled={
            //   tableDs.selected?.length !== 1 ||
            //   (tableDs.selected?.length === 1 && !tableDs?.selected[0]?.get('newEoNumber'))}
            // 多条打印限制
            disabled={
              tableDs.selected?.length === 0 || 
              tableDs.selected.some((item)=>{
                return !item.get('newEoNumber')
              })
            }
            onClick={handleReprintCertificate}
          >
            打印合格证
          </Button>
          <div style={{ display: 'none' }}>
            {repPrintStatus && (
              <TemplatePrintButton
                printButtonCode={printTemplate.current}
                icon=""
                ref={repPrint}
                style={{
                  backgroundColor:
                    tableDs.selected?.length !== 1 ||
                    (tableDs.selected?.length === 1 && !tableDs?.selected[0]?.get('newEoNumber'))
                      ? '#bfbfbf'
                      : '#00d4cd',
                }}
                disabled={
                  tableDs.selected?.length !== 1 ||
                  (tableDs.selected?.length === 1 && !tableDs?.selected[0]?.get('newEoNumber'))
                }
                name={intl.get(`${modelPrompt}.print`).d('打印合格证')}
                printParams={repPrintParams}
                frUrl="/roll-work-order-card/print"
                method="POST"
              />
            )}
          </div>
        </div>
        <Button
          color={ButtonColor.primary}
          className={styles.buttonOk}
          onClick={handleTransactionSummary}
        >
          事务汇总
        </Button>
      </div>
    </CardLayout.Layout>
  );
});

export default RollOverWorkOrder;
