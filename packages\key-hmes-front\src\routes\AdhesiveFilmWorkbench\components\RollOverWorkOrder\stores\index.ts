/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-17 11:31:28
 * @LastEditTime: 2023-07-20 10:37:38
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import moment from 'moment';

// BASIC.HMES_BASIC = "dst-mes-29210"
const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();
const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  paging: false,
  dataKey: 'rows',
  queryFields: [
    {
      name: 'startTime',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.startTime`).d('日期从'),
      required: true,
      max: 'endTime',
      defaultValue: moment(new Date()).format('YYYY-MM-DD 00:00:00'),
      transformRequest: value => {
        return value ? moment(value).format('YYYY-MM-DD 00:00:00') : value;
      },
    },
    {
      name: 'endTime',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.endTime`).d('至'),
      min: 'startTime',
      required: true,
      defaultValue: moment(new Date()).format('YYYY-MM-DD 23:59:59'),
      transformRequest: value => {
        return value ? moment(value).format('YYYY-MM-DD 23:59:59') : value;
      },
    },
  ],
  fields: [
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令单'),
    },
    {
      name: 'oldEoNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.oldEoNumber`).d('原卷号'),
    },
    {
      name: 'workOrderStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderStatusDesc`).d('状态'),
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planStartTime`).d('日期'),
    },
    {
      name: 'oldProdModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.oldProdModel`).d('原产品规格'),
    },
    {
      name: 'newProdModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.newProdModel`).d('新产品规格'),
    },
    {
      name: 'prodDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodDesc`).d('产线'),
    },
    {
      name: 'prodTargetValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodTargetValue`).d('目标厚度'),
    },
    {
      name: 'color',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.color`).d('颜色'),
    },
    {
      name: 'newEoNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.newEoNumber`).d('新卷号'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/demotion-work-order-card/select/list`,
        method: 'GET',
      };
    },
  },
});
const FormDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'oldProdModel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.oldProdModel`).d('产品规格'),
      disabled: true,
    },
    {
      name: 'oldEoNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.oldEoNumber`).d('原卷号'),
      disabled: true,
    },
    {
      name: 'newEoNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.newEoNumber`).d('新卷号'),
      disabled: true,
    },
    {
      name: 'planWeight',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planWeight`).d('计划重量'),
      disabled: true,
      precision: 6,
    },
    {
      name: 'actualWeight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.actualWeight`).d('实际重量'),
      required: true,
      precision: 3,
    },
  ],
});

const oldEoNumberFormDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'eoNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.oldEoNumber`).d('原卷号'),
      disabled: true,
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('EO标识'),
      disabled: true,
    },
    {
      name: 'defectInformation1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectInformation1`).d('杂质1'),
      disabled: true,
    },
    {
      name: 'defectInformation2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectInformation2`).d('杂质2'),
      disabled: true,
    },
    {
      name: 'defectInformation3',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectInformation3`).d('杂质3'),
      disabled: true,
    },
    {
      name: 'defectInformation4',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.defectInformation4`).d('杂质4'),
      disabled: true,
    },
    {
      name: 'joint1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.joint1`).d('接头1'),
      disabled: true,
    },
    {
      name: 'joint2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.joint2`).d('接头2'),
      disabled: true,
    },
  ],
});

const newEoNumberFormDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'newEoNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.newEoNumber`).d('新卷号'),
    },
    {
      name: 'newIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.newIdentification`).d('新EO标识'),
    },
    {
      name: 'newDefectInformation1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.newDefectInformation1`).d('新杂质1'),
    },
    {
      name: 'newDefectInformation2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.newDefectInformation2`).d('新杂质2'),
    },
    {
      name: 'newDefectInformation3',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.newDefectInformation3`).d('新杂质3'),
    },
    {
      name: 'newDefectInformation4',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.newDefectInformation4`).d('新杂质4'),
    },
    {
      name: 'newJoint1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.newJoint1`).d('新接头1'),
    },
    {
      name: 'newJoint2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.newJoint2`).d('新接头2'),
    },
  ],
});

export { tableDS, FormDS, oldEoNumberFormDS, newEoNumberFormDS };
